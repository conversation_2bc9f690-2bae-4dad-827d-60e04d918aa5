import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/utils/env_var.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/utils/check_update.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pubspec_parse/pubspec_parse.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';

class About extends StatefulWidget {
  const About({
    super.key,
    this.leadingColor = false,
  });
  final bool leadingColor;

  @override
  State<About> createState() => _AboutState();
}

class _AboutState extends State<About> {
  String version = '';

  @override
  void initState() {
    super.initState();
    initData();
  }

  Future<void> initData() async {
    final pubspecContent = await rootBundle.loadString('pubspec.yaml');
    final pubspec = Pubspec.parse(pubspecContent);
    setState(() {
      version = pubspec.version.toString();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(L10n.of(context).app_about),
      leading: Icon(
        AdaptiveIcons.info,
        color:
            widget.leadingColor ? Theme.of(context).colorScheme.primary : null,
      ),
      onTap: () => openAboutDialog(context),
    );
  }

  void openAboutDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: DesignSystem.getAdaptiveDialogWidth(context),
              minWidth: DesignSystem.isDesktop(context) ? 300 : 250,
            ),
            child: SingleChildScrollView(
              child: SizedBox(
                width: DesignSystem.getAdaptiveDialogWidth(context),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                        bottom: DesignSystem.spaceXS + 1,
                      ),
                      child: Center(
                        child: Text(
                          'DassoShu',
                          style: TextStyle(
                            fontSize: DesignSystem.fontSizeDisplayL,
                            fontWeight: DesignSystem.getAdjustedFontWeight(
                              FontWeight.bold,
                            ),
                            color: DesignSystem.getSettingsTextColor(
                              context,
                              isPrimary: true,
                            ),
                          ),
                        ),
                      ),
                    ),
                    // App description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignSystem.spaceM,
                        vertical: DesignSystem.spaceS,
                      ),
                      child: Text(
                        L10n.of(context).app_description,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: DesignSystem.fontSizeS,
                          color: DesignSystem.getSettingsTextColor(context),
                        ),
                      ),
                    ),
                    const Divider(),
                    ListTile(
                      title: Text(L10n.of(context).app_version),
                      subtitle: Text(version + (kDebugMode ? ' (debug)' : '')),
                      onTap: () {
                        Clipboard.setData(ClipboardData(text: version));
                        AnxToast.show(L10n.of(context).notes_page_copied);
                      },
                    ),
                    if (!EnvVar.isAppStore)
                      ListTile(
                        title: Text(L10n.of(context).about_check_for_updates),
                        onTap: () => checkUpdate(true),
                      ),
                    // Feature highlights for DassoShu Reader
                    ExpansionTile(
                      title: Text(L10n.of(context).app_features),
                      leading: Icon(
                        Icons.star_outline,
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: true,
                        ),
                      ),
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: DesignSystem.spaceL,
                            vertical: DesignSystem.spaceS,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildFeatureItem(
                                context,
                                AdaptiveIcons.translate,
                                L10n.of(context).app_feature_chinese_learning,
                              ),
                              _buildFeatureItem(
                                context,
                                AdaptiveIcons.book,
                                L10n.of(context).app_feature_epub_support,
                              ),
                              _buildFeatureItem(
                                context,
                                Icons.auto_awesome,
                                L10n.of(context).app_feature_ai_assistance,
                              ),
                              _buildFeatureItem(
                                context,
                                AdaptiveIcons.sync,
                                L10n.of(context).app_feature_cross_platform,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    // Author information for DassoShu Reader
                    ListTile(
                      title: Text(L10n.of(context).app_author),
                      subtitle: Text(L10n.of(context).app_author_info),
                      leading: Icon(
                        Icons.person_outline,
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: true,
                        ),
                      ),
                      onTap: () => _showAuthorDialog(context),
                    ),
                    ListTile(
                      title: Text(L10n.of(context).app_license),
                      onTap: () {
                        showLicensePage(
                          context: context,
                          applicationName: 'DassoShu Reader',
                          applicationVersion: version,
                        );
                      },
                    ),
                    // Removed external links to Anx Reader
                    // DassoShu Reader is focused on Chinese language learning
                    if (EnvVar.isBeian) const Divider(),
                    if (EnvVar.isBeian)
                      GestureDetector(
                        onTap: () {
                          launchUrl(
                            Uri.parse('https://beian.miit.gov.cn/'),
                            mode: LaunchMode.externalApplication,
                          );
                        },
                        child: const Text('闽ICP备**********号-1A'),
                      ),
                    if (!EnvVar.isBeian) const Divider(),
                    // Educational mission statement for DassoShu Reader
                    Padding(
                      padding: const EdgeInsets.all(DesignSystem.spaceM),
                      child: Text(
                        L10n.of(context).app_educational_mission,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontStyle: FontStyle.italic,
                          fontSize: DesignSystem.fontSizeS,
                          color: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: true,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Helper method to build feature items for the About dialog
  Widget _buildFeatureItem(
    BuildContext context,
    IconData icon,
    String text,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceXS),
      child: Row(
        children: [
          Icon(
            icon,
            size: DesignSystem.fontSizeL,
            color: DesignSystem.getSettingsTextColor(
              context,
              isPrimary: true,
            ),
          ),
          const SizedBox(width: DesignSystem.spaceS),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: DesignSystem.fontSizeS,
                color: DesignSystem.getSettingsTextColor(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show detailed author information dialog
  void _showAuthorDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.person,
                color: DesignSystem.getSettingsTextColor(
                  context,
                  isPrimary: true,
                ),
              ),
              const SizedBox(width: DesignSystem.spaceS),
              Text(L10n.of(context).app_author),
            ],
          ),
          content: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: DesignSystem.getAdaptiveDialogWidth(context),
              minWidth: DesignSystem.isDesktop(context) ? 300 : 250,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Author names
                Center(
                  child: Column(
                    children: [
                      Text(
                        L10n.of(context).app_author_name,
                        style: TextStyle(
                          fontSize: DesignSystem.fontSizeL,
                          fontWeight: DesignSystem.getAdjustedFontWeight(
                            FontWeight.w600,
                          ),
                          color: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: true,
                          ),
                        ),
                      ),
                      const SizedBox(height: DesignSystem.spaceXS),
                      Text(
                        L10n.of(context).app_author_chinese_name,
                        style: TextStyle(
                          fontSize: DesignSystem.fontSizeChineseM,
                          fontWeight: DesignSystem.getAdjustedFontWeight(
                            FontWeight.w500,
                          ),
                          color: DesignSystem.getSettingsTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: DesignSystem.spaceM),

                // Author description
                Text(
                  L10n.of(context).app_author_description,
                  style: TextStyle(
                    fontSize: DesignSystem.fontSizeS,
                    color: DesignSystem.getSettingsTextColor(context),
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: DesignSystem.spaceM),

                // Author mission
                Container(
                  padding: const EdgeInsets.all(DesignSystem.spaceS),
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(DesignSystem.radiusS),
                  ),
                  child: Text(
                    L10n.of(context).app_author_mission,
                    style: TextStyle(
                      fontSize: DesignSystem.fontSizeS,
                      fontStyle: FontStyle.italic,
                      color: DesignSystem.getSettingsTextColor(
                        context,
                        isPrimary: true,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(L10n.of(context).common_ok),
            ),
          ],
        );
      },
    );
  }
}
