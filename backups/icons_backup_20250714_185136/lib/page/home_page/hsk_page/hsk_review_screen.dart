import 'dart:async';
import 'dart:math';

import 'package:dasso_reader/config/color_system.dart';
import 'package:dasso_reader/models/hsk_character.dart';
import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/models/hsk_settings.dart';
import 'package:dasso_reader/providers/hsk_providers.dart';
import 'package:dasso_reader/config/typography.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:dasso_reader/widgets/common/adaptive_components.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:dasso_reader/widgets/responsive_button_text.dart';

/// A screen that provides a review experience for HSK characters.
///
/// It shows characters one by one, allowing the user to reveal their pinyin and English
/// translations, and mark whether they knew the character or need to review it again.
class HskReviewScreen extends ConsumerStatefulWidget {
  final HskCharacterSet characterSet;

  const HskReviewScreen({
    super.key,
    required this.characterSet,
  });

  @override
  ConsumerState<HskReviewScreen> createState() => _HskReviewScreenState();
}

class _HskReviewScreenState extends ConsumerState<HskReviewScreen> {
  // Character state
  List<HskCharacter> _itemOrderList = [];
  late HskCharacter _currentCharacter;
  int _currentIndex = 0;
  int _repeatsCount = 0;
  int _viewsCount = 0;
  late int _totalCards;

  // UI state
  bool _showingAnswer = false;
  bool _autoPlaySound = false;
  bool _showPinyin = true;
  bool _showEnglish = true;

  // Audio
  final AudioPlayer _audioPlayer = AudioPlayer();

  // Audio error handling (like HskLearnScreen)
  bool _audioError = false;

  // Notifier references for proper disposal without using ref in dispose
  HskSessionProgress? _sessionProgressNotifier;
  CharacterProgress? _characterProgressNotifier;

  // =====================================================
  // STATIC OPTIMIZED COMPONENTS - Performance Enhancement
  // =====================================================

  /// Theme-aware background gradient with WCAG AAA compliance
  BoxDecoration _getBackgroundGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          colorScheme.primaryContainer,
          colorScheme.secondaryContainer,
        ],
      ),
    );
  }

  /// Get container color from theme - matches HSK Set Details methodology
  Color get _containerColor => Theme.of(context)
      .colorScheme
      .surfaceContainerHighest
      .withValues(alpha: 0.8);

  /// Get audio button colors from theme
  Color get _audioButtonColor => Theme.of(context).colorScheme.primary;
  Color get _audioErrorColor => Theme.of(context).colorScheme.error;
  Color get _audioButtonIconColor => Theme.of(context).colorScheme.onPrimary;
  Color get _audioErrorIconColor => Theme.of(context).colorScheme.onError;

  // --- UI Constants for Responsive Design - Using DesignSystem ---
  static const double _kCharacterDisplaySize =
      72.0; // Increased for better visibility in review mode
  static const double _kPinyinFontSize = 28.0; // Larger for review mode
  static const double _kEnglishFontSize = 24.0; // Larger for review mode

  // Container constraints
  static const double _kCharacterContainerMinHeight =
      140.0; // Increased for larger text
  static const double _kCharacterContainerMaxHeight =
      200.0; // Increased for larger text
  static const double _kAnswerContainerMinHeight =
      120.0; // Increased for larger text
  static const double _kAnswerContainerMaxHeight =
      220.0; // Increased for larger text

  // Button dimensions - Using DesignSystem
  static const double _kButtonHeight = 60.0;
  static const double _kButtonFontSize = 18.0;
  static double get _kButtonBorderRadius =>
      DesignSystem.radiusM - 4; // 12.0 (preserves exact radius)

  // Spacing and padding - Using DesignSystem
  static EdgeInsets get _kContainerPadding => const EdgeInsets.symmetric(
        vertical: DesignSystem.spaceL, // 20.0 (preserves exact spacing)
        horizontal: DesignSystem.spaceM, // 16.0 (preserves exact spacing)
      );
  static EdgeInsets get _kMainPadding => const EdgeInsets.all(
        DesignSystem.spaceM,
      ); // 16.0 (preserves exact spacing)
  static double get _kMainSpacing =>
      DesignSystem.spaceL + 4; // 24.0 (preserves exact spacing)
  static double get _kButtonSpacing =>
      DesignSystem.spaceM - 4; // 12.0 (preserves exact spacing)

  @override
  void initState() {
    super.initState();

    // Store notifier references for proper disposal
    _sessionProgressNotifier = ref.read(hskSessionProgressProvider.notifier);
    _characterProgressNotifier = ref.read(characterProgressProvider.notifier);

    // Initialize characters in random order
    _initializeCharacters();

    // Start the session
    Future.microtask(() {
      _sessionProgressNotifier?.startLearnSession(_totalCards);
    });

    // Start first round
    _startNewRound();
  }

  void _initializeCharacters() {
    // Get characters from the set
    List<HskCharacter> characters = widget.characterSet.characters.toList();

    // Shuffle the characters for random order
    characters.shuffle();

    // Limit to 30 cards maximum, like in the Java version
    _totalCards = min(characters.length, 30);
    _itemOrderList = characters.take(_totalCards).toList();

    // Set the first character
    _currentCharacter = _itemOrderList[0];
  }

  @override
  void dispose() {
    _audioPlayer.dispose();

    // End the session and update total view time safely
    // Store notifier references before disposal to avoid ref access in dispose
    try {
      if (mounted) {
        final sessionNotifier = _sessionProgressNotifier;
        final characterNotifier = _characterProgressNotifier;

        if (sessionNotifier != null) {
          sessionNotifier.endSession();
        }

        if (characterNotifier != null) {
          characterNotifier.updateCharacter(
            _currentCharacter.copyWith(
              viewCount: _currentCharacter.viewCount + _viewsCount,
            ),
          );
        }
      }
    } catch (e) {
      // Widget already disposed, ignore the error
      if (kDebugMode) {
        debugPrint(
          'HSK Review Screen: Session already ended or widget disposed',
        );
      }
    }

    super.dispose();
  }

  void _startNewRound() {
    setState(() {
      _currentCharacter = _itemOrderList[_currentIndex];
      _showingAnswer = false;
    });

    if (_autoPlaySound) {
      _playAudio();
    }
  }

  void _showAnswer() {
    setState(() {
      _showingAnswer = true;
      _viewsCount++;
    });
  }

  void _handleGoodPress() {
    if (_showingAnswer) {
      // Go to next card
      _goToNextCard();
    } else {
      // Reveal the answer
      _showAnswer();
    }
  }

  void _handleAgainPress() {
    // Move this card 5 positions later in the deck
    _moveCardLater();
    _repeatsCount++;
    _goToNextCard();
  }

  void _goToNextCard() {
    _currentIndex++;

    if (_currentIndex >= _totalCards) {
      // End the review session
      _navigateToCompletionScreen();
    } else {
      // Show the next card
      _startNewRound();
    }
  }

  void _moveCardLater() {
    // Similar to the Java 'proder' method
    int cardsToMove = 5;

    // Handle the case when we're near the end of the deck
    if (_currentIndex >= _totalCards - 5) {
      cardsToMove = _totalCards - _currentIndex - 1;
    }

    if (cardsToMove <= 0) return;

    // Store the current character
    HskCharacter currentCard = _currentCharacter;

    // Move cards forward
    for (int i = _currentIndex; i < _currentIndex + cardsToMove; i++) {
      _itemOrderList[i] = _itemOrderList[i + 1];
    }

    // Put the current card at the new position
    _itemOrderList[_currentIndex + cardsToMove] = currentCard;

    // Decrement the index since we'll increment it in goToNextCard
    _currentIndex--;
  }

  void _navigateToCompletionScreen() {
    if (!mounted) return;

    // Calculate session duration
    final sessionDuration =
        _sessionProgressNotifier?.getSessionDurationSeconds() ?? 0;

    // Navigate to the completion screen (now using our custom screen)
    AdaptiveNavigation.pushReplacement(
      context,
      HskReviewCompleteScreen(
        characterSet: widget.characterSet,
        repeats: _repeatsCount,
        duration: sessionDuration,
      ),
    );
  }

  void _playAudio() async {
    try {
      // Reset error state
      setState(() {
        _audioError = false;
      });

      // Stop any current playback
      await _audioPlayer.stop();

      // Get the audio path
      final audioPath = _currentCharacter.audioAssetPath;

      // Set volume to max
      await _audioPlayer.setVolume(1.0);

      // Play audio with asset source
      await _audioPlayer.play(AssetSource(audioPath));
    } catch (e) {
      // Try alternate path format as fallback
      try {
        final hskNum =
            _currentCharacter.hskLevel.replaceAll(RegExp(r'[^0-9]'), '');
        final fallbackPath =
            'audio/hsk${hskNum}_opus/${_currentCharacter.characterId}.opus';

        await _audioPlayer.play(AssetSource(fallbackPath));
      } catch (fallbackError) {
        // Set error state to show visual indicator
        setState(() {
          _audioError = true;
        });

        debugPrint(
            'Error playing audio for character: ${_currentCharacter.character}, '
            'Path: ${_currentCharacter.audioAssetPath}');
      }
    }
  }

  void _showSettingsDialog() async {
    try {
      // Create default settings for use if needed
      const defaultSettings = HskReviewSettings(
        autoPlaySound: false,
        showPinyin: true,
        showEnglish: true,
      );

      final currentSettings = await ref
          .read(hskReviewSettingsProvider.future)
          .catchError((Object e) {
        debugPrint('Error in _showSettingsDialog: $e');
        return defaultSettings;
      });

      if (!mounted) return;

      showDialog<void>(
        context: context,
        builder: (context) => ReviewSettingsDialog(
          initialSettings: currentSettings,
          onSettingsChanged: (newSettings) {
            // Update local state immediately (the provider might fail)
            setState(() {
              _autoPlaySound = newSettings.autoPlaySound;
              _showPinyin = newSettings.showPinyin;
              _showEnglish = newSettings.showEnglish;
            });

            // Try to save to provider (but continue even if it fails)
            try {
              // Play audio if setting was just enabled
              if (_autoPlaySound && !currentSettings.autoPlaySound) {
                _playAudio();
              }
            } catch (e) {
              debugPrint('Error updating settings: $e');
            }
          },
        ),
      );
    } catch (e) {
      debugPrint('Error showing settings dialog: $e');

      if (!mounted) return;

      // Show a simple dialog if the proper settings dialog fails
      AdaptiveDialogs.showAlert<void>(
        context: context,
        title: 'Settings',
        content: 'Something went wrong loading settings.',
        actions: [
          AdaptiveDialogAction(
            text: 'OK',
            onPressed: () => Navigator.pop(context),
          ),
        ],
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Review Mode',
          style: TextStyle(
            color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
          ),
        ),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(),
          ),
        ],
      ),
      body: Container(
        decoration: _getBackgroundGradient(context),
        child: SafeArea(
          child: Column(
            children: [
              // Progress indicator
              Padding(
                padding: const EdgeInsets.all(
                  DesignSystem.spaceS,
                ), // 8.0 (preserves exact spacing)
                child: Text(
                  '${_currentIndex + 1}/$_totalCards',
                  style: TextStyle(
                    color: DesignSystem.getSettingsTextColor(
                      context,
                      isPrimary: false,
                    ),
                    fontWeight: FontWeight.bold,
                    fontSize: DesignSystem.fontSizeL,
                  ),
                ),
              ),

              // Main content with responsive layout (ORIGINAL STRUCTURE RESTORED)
              Expanded(
                child: Padding(
                  padding: _kMainPadding,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Character card with responsive sizing (ORIGINAL)
                      Container(
                        width: double.infinity,
                        constraints: const BoxConstraints(
                          minHeight: _kCharacterContainerMinHeight,
                          maxHeight: _kCharacterContainerMaxHeight,
                        ),
                        padding: _kContainerPadding,
                        decoration: BoxDecoration(
                          color: _containerColor,
                          borderRadius: BorderRadius.circular(
                            DesignSystem.radiusM,
                          ), // 16.0 (preserves exact radius)
                        ),
                        clipBehavior: Clip
                            .hardEdge, // Ensure content is clipped to bounds
                        child: Center(
                          child: ResponsiveButtonText(
                            character: _currentCharacter.character,
                            characterFontSize: _kCharacterDisplaySize,
                            maxWidth:
                                ResponsiveSystem.getScreenWidth(context) * 0.8,
                            maxHeight: _kCharacterContainerMaxHeight -
                                (_kContainerPadding.vertical * 2),
                          ),
                        ),
                      ),

                      SizedBox(height: _kMainSpacing),

                      // Answer area with responsive container (ORIGINAL)
                      Container(
                        width: double.infinity,
                        constraints: const BoxConstraints(
                          minHeight: _kAnswerContainerMinHeight,
                          maxHeight: _kAnswerContainerMaxHeight,
                        ),
                        padding: _kContainerPadding,
                        decoration: BoxDecoration(
                          color: _containerColor,
                          borderRadius: BorderRadius.circular(
                            DesignSystem.radiusM,
                          ), // 16.0 (preserves exact radius)
                        ),
                        clipBehavior: Clip
                            .hardEdge, // Ensure content is clipped to bounds
                        child: Center(
                          child: _showingAnswer
                              ? _buildAnswerContent()
                              : const SizedBox(), // Empty but maintains container size
                        ),
                      ),

                      const Spacer(),

                      // Action buttons with improved responsive layout (ORIGINAL)
                      _buildActionButtons(),
                    ],
                  ),
                ),
              ),

              // Audio button positioned at bottom center (ORIGINAL WORKING LAYOUT RESTORED)
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(
                    right: DesignSystem.spaceM -
                        4, // 12.0 (preserves exact spacing)
                    bottom:
                        DesignSystem.spaceM, // 16.0 (preserves exact spacing)
                  ),
                  child: _buildAudioButton(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the answer content with responsive text handling (ORIGINAL)
  Widget _buildAnswerContent() {
    // Use the responsive widget for consistent text handling across all screens
    return ResponsiveButtonText(
      pinyin: _currentCharacter.pinyin,
      english: _currentCharacter.englishTranslation,
      showPinyin: _showPinyin,
      showEnglish: _showEnglish,
      pinyinFontSize: _kPinyinFontSize,
      englishFontSize: _kEnglishFontSize,
      maxEnglishLines:
          3, // Allow more lines in review mode for better readability
      maxWidth:
          ResponsiveSystem.getScreenWidth(context) * 0.8, // 80% of screen width
      maxHeight: _kAnswerContainerMaxHeight - (_kContainerPadding.vertical * 2),
    );
  }

  /// Builds the action buttons with responsive layout
  Widget _buildActionButtons() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceM,
      ), // 16.0 equivalent
      child: _showingAnswer
          ? Row(
              children: [
                // Good button
                Expanded(
                  child: _buildActionButton(
                    text: 'Good',
                    buttonType: ButtonType.success,
                    onPressed: _handleGoodPress,
                  ),
                ),
                SizedBox(width: _kButtonSpacing),
                // Again button
                Expanded(
                  child: _buildActionButton(
                    text: 'Again',
                    buttonType: ButtonType.neutral,
                    onPressed: _handleAgainPress,
                  ),
                ),
              ],
            )
          : _buildActionButton(
              text: 'Show',
              buttonType: ButtonType.primary,
              onPressed: _handleGoodPress,
              isFullWidth: true,
            ),
    );
  }

  // Helper to build consistent responsive buttons
  Widget _buildActionButton({
    required String text,
    required ButtonType buttonType,
    required VoidCallback onPressed,
    bool isFullWidth = false,
  }) {
    final buttonColors = ColorSystem.getButtonColors(context, buttonType);

    return SizedBox(
      width: isFullWidth ? double.infinity : null,
      height: _kButtonHeight,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: buttonColors.background,
          foregroundColor: buttonColors.foreground,
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceM, // 16.0 (preserves exact spacing)
            vertical: DesignSystem.spaceM, // 16.0 (preserves exact spacing)
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_kButtonBorderRadius),
          ),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            text,
            style: const TextStyle(
              fontSize: _kButtonFontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  /// Builds the floating action button for replaying audio (matching HskLearnScreen design)
  Widget _buildAudioButton() {
    return Container(
      margin: const EdgeInsets.only(
        bottom: DesignSystem.spaceXS,
      ), // 4.0 (preserves exact spacing)
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Main audio button
          Material(
            color: _audioButtonColor,
            shape: const CircleBorder(),
            elevation: DesignSystem.elevationS,
            child: InkWell(
              onTap: _playAudio,
              customBorder: const CircleBorder(),
              child: Padding(
                padding: const EdgeInsets.all(
                  DesignSystem.spaceS,
                ), // 8.0 (preserves exact spacing)
                child: Icon(
                  Icons.volume_up,
                  color: _audioError
                      ? _audioErrorIconColor
                      : _audioButtonIconColor,
                  size: DesignSystem.widgetIconSizeMedium, // Slightly smaller
                ),
              ),
            ),
          ),

          // Subtle error indicator
          if (_audioError)
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                width: 12, // Slightly smaller
                height: 12, // Slightly smaller
                decoration: BoxDecoration(
                  color: _audioErrorColor,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  size: 8, // Slightly smaller
                  color: DesignSystem.getSettingsTextColor(
                    context,
                    isPrimary: true,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Dialog for configuring HSK review mode settings.
///
/// Allows users to toggle options such as auto-playing audio,
/// visibility of pinyin and English translations.
class ReviewSettingsDialog extends StatefulWidget {
  final HskReviewSettings initialSettings;
  final void Function(HskReviewSettings) onSettingsChanged;

  const ReviewSettingsDialog({
    super.key,
    required this.initialSettings,
    required this.onSettingsChanged,
  });

  @override
  State<ReviewSettingsDialog> createState() => _ReviewSettingsDialogState();
}

class _ReviewSettingsDialogState extends State<ReviewSettingsDialog> {
  late bool _autoPlaySound;
  late bool _showPinyin;
  late bool _showEnglish;

  @override
  void initState() {
    super.initState();
    _autoPlaySound = widget.initialSettings.autoPlaySound;
    _showPinyin = widget.initialSettings.showPinyin;
    _showEnglish = widget.initialSettings.showEnglish;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Review Mode Settings',
        style: TextStyle(
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CheckboxListTile(
              title: const Text('Auto Play Sound'),
              value: _autoPlaySound,
              onChanged: (value) {
                setState(() {
                  _autoPlaySound = value ?? false;
                });
              },
            ),
            CheckboxListTile(
              title: const Text('Show Pinyin'),
              value: _showPinyin,
              onChanged: (value) {
                setState(() {
                  _showPinyin = value ?? true;
                });
              },
            ),
            CheckboxListTile(
              title: const Text('Show English'),
              value: _showEnglish,
              onChanged: (value) {
                setState(() {
                  _showEnglish = value ?? true;
                });
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: const Text('CANCEL'),
        ),
        TextButton(
          onPressed: () {
            final newSettings = HskReviewSettings(
              autoPlaySound: _autoPlaySound,
              showPinyin: _showPinyin,
              showEnglish: _showEnglish,
            );
            widget.onSettingsChanged(newSettings);
            Navigator.of(context).pop();
          },
          child: const Text('OKAY'),
        ),
      ],
    );
  }
}

// Add a dedicated Review completion screen
class HskReviewCompleteScreen extends StatelessWidget {
  final HskCharacterSet characterSet;
  final int repeats;
  final int duration;

  // =====================================================
  // STATIC OPTIMIZED COMPONENTS - Performance Enhancement
  // =====================================================

  /// Get background gradient from theme
  BoxDecoration _getBackgroundGradient(BuildContext context) => BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.secondary,
          ],
        ),
      );

  // --- UI Constants for Responsive Design - Using DesignSystem ---
  static const double _kTitleFontSize = 36.0;
  static const double _kMessageFontSize = 18.0;
  static const double _kStatsFontSize = 28.0;
  static const double _kButtonFontSize = 18.0;
  static const double _kButtonHeight = 60.0;
  static double get _kButtonBorderRadius =>
      DesignSystem.radiusM - 4; // 12.0 (preserves exact radius)
  static double get _kMainSpacing =>
      DesignSystem.spaceL + 4; // 24.0 (preserves exact spacing)
  static double get _kStatsSpacing =>
      DesignSystem.spaceM; // 16.0 (preserves exact spacing)
  static double get _kButtonSpacing =>
      DesignSystem.spaceM - 4; // 12.0 (preserves exact spacing)
  static EdgeInsets get _kMainPadding => const EdgeInsets.all(
        DesignSystem.spaceL + 4,
      ); // 24.0 (preserves exact spacing)
  static EdgeInsets get _kButtonPadding => const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceM, // 16.0 (preserves exact spacing)
        vertical: DesignSystem.spaceM, // 16.0 (preserves exact spacing)
      );

  const HskReviewCompleteScreen({
    super.key,
    required this.characterSet,
    required this.repeats,
    required this.duration,
  });

  /// Get button colors from theme
  Color _getReturnButtonColor(BuildContext context) =>
      Theme.of(context).colorScheme.outline;
  Color _getAgainButtonColor(BuildContext context) =>
      Theme.of(context).colorScheme.secondary;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: _getBackgroundGradient(context),
        child: SafeArea(
          child: Padding(
            padding: _kMainPadding,
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Spacer(flex: 1),

                    // Title with responsive sizing
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        'Review Completed',
                        style: HskTypography.withSize(
                          HskTypography.completionTitle,
                          _kTitleFontSize,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    SizedBox(height: _kMainSpacing),

                    // Message with responsive container
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth * 0.9,
                      ),
                      child: Text(
                        'Well done. You have successfully reviewed this set of characters. Tap the return button to go back to the main menu or tap the again button to review again.',
                        textAlign: TextAlign.center,
                        style: HskTypography.withSize(
                          HskTypography.completionDescription,
                          _kMessageFontSize,
                        ),
                        maxLines: 4,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    const Spacer(flex: 1),

                    // Statistics with responsive layout
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth * 0.8,
                      ),
                      child: Column(
                        children: [
                          // Statistics - Repeats
                          FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              'Repeats $repeats',
                              style: HskTypography.withSize(
                                HskTypography.completionStats,
                                _kStatsFontSize,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),

                          SizedBox(height: _kStatsSpacing),

                          // Statistics - Time spent
                          FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(
                              'Time spent ${_formatTime(duration)}',
                              style: HskTypography.withSize(
                                HskTypography.completionStats,
                                _kStatsFontSize,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(flex: 2),

                    // Buttons with improved responsive layout
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth * 0.9,
                      ),
                      child: Row(
                        children: [
                          // Return button
                          Expanded(
                            child: _buildActionButton(
                              context: context,
                              text: 'Return',
                              color: _getReturnButtonColor(context),
                              onPressed: () {
                                // Pop back to HskSetDetailsScreen (consistent with Learn mode)
                                Navigator.pop(context);
                              },
                            ),
                          ),

                          SizedBox(width: _kButtonSpacing),

                          // Again button
                          Expanded(
                            child: _buildActionButton(
                              context: context,
                              text: 'Again',
                              color: _getAgainButtonColor(context),
                              onPressed: () {
                                AdaptiveNavigation.pushReplacement(
                                  context,
                                  HskReviewScreen(
                                    characterSet: characterSet,
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(flex: 1),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  /// Builds consistent responsive action buttons with WCAG AAA compliance
  Widget _buildActionButton({
    required BuildContext context,
    required String text,
    required Color color,
    required VoidCallback onPressed,
  }) {
    // Ensure proper contrast for button text - use white text for better visibility
    Color textColor;
    if (text == 'Return') {
      // Return button: ensure white text for proper contrast against dark outline color
      textColor = Colors.white;
    } else {
      // Again button: use theme-appropriate color for secondary button
      textColor = Theme.of(context).colorScheme.onSecondary;
    }

    return SizedBox(
      height: _kButtonHeight,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: textColor,
          padding: _kButtonPadding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_kButtonBorderRadius),
          ),
        ),
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: Text(
            text,
            style: const TextStyle(
              fontSize: _kButtonFontSize,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  String _formatTime(int seconds) {
    if (seconds < 60) {
      return '${seconds}s';
    } else if (seconds < 3600) {
      final minutes = seconds ~/ 60;
      final remainingSeconds = seconds % 60;
      return "$minutes min ${remainingSeconds > 0 ? '$remainingSeconds sec' : ''}";
    } else {
      final hours = seconds ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;
      return "$hours h ${minutes > 0 ? '$minutes min' : ''}";
    }
  }
}
