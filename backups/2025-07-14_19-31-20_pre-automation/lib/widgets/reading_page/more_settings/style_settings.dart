import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_style.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/widgets/icon_and_text.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';

class StyleSettings extends StatefulWidget {
  const StyleSettings({super.key});

  @override
  State<StyleSettings> createState() => _StyleSettingsState();
}

class _StyleSettingsState extends State<StyleSettings> {
  // Create a common slider theme to be used across all sliders
  SliderThemeData _getSliderTheme(
    Color txtColor, {
    String Function(double)? valueToString,
  }) {
    return SliderThemeData(
      trackHeight: 36.0,
      thumbShape: TextThumbShape(
        thumbRadius: 18.0,
        textStyle: TextStyle(
          fontSize: DesignSystem.fontSizeS,
          fontWeight: FontWeight.bold,
          color: txtColor,
        ),
        valueToString: valueToString,
      ),
      overlayShape: const RoundSliderOverlayShape(overlayRadius: 26.0),
      trackShape: const RoundedRectSliderTrackShape(),
      activeTrackColor: txtColor.withValues(alpha: 0.25),
      inactiveTrackColor: txtColor.withAlpha(25),
      thumbColor: Colors.white,
      overlayColor: txtColor.withAlpha(50),
      showValueIndicator: ShowValueIndicator.never,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget textIndent(BookStyle bookStyle, StateSetter setState) {
      final txtColor = Theme.of(context).colorScheme.onSurface;

      return Row(
        children: [
          IconAndText(
            icon: Icon(AdaptiveIcons.formatIndentIncrease),
            text: L10n.of(context).reading_page_indent,
          ),
          Expanded(
            child: SliderTheme(
              data: _getSliderTheme(
                txtColor,
                valueToString: (value) => value.toStringAsFixed(1),
              ),
              child: Slider(
                padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
                value: bookStyle.indent,
                onChanged: (double value) {
                  setState(() {
                    bookStyle.indent = value;
                    epubPlayerKey.currentState?.changeStyle(bookStyle);
                    Prefs().saveBookStyleToPrefs(bookStyle);
                  });
                },
                min: 0,
                max: 8,
                divisions: 16,
                label: bookStyle.indent.toStringAsFixed(1),
              ),
            ),
          ),
        ],
      );
    }

    Widget letterSpacingSlider(BookStyle bookStyle, StateSetter setState) {
      final txtColor = Theme.of(context).colorScheme.onSurface;

      return Row(
        children: [
          IconAndText(
            icon: Icon(AdaptiveIcons.compareArrows),
            text: L10n.of(context).reading_page_letter_spacing,
          ),
          Expanded(
            child: SliderTheme(
              data: _getSliderTheme(
                txtColor,
                valueToString: (value) => value.toString(),
              ),
              child: Slider(
                padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
                value: bookStyle.letterSpacing,
                onChanged: (double value) {
                  setState(() {
                    bookStyle.letterSpacing = value;
                    epubPlayerKey.currentState?.changeStyle(bookStyle);
                    Prefs().saveBookStyleToPrefs(bookStyle);
                  });
                },
                min: -3,
                max: 7,
                divisions: 10,
                label: (bookStyle.letterSpacing).toString(),
              ),
            ),
          ),
        ],
      );
    }

    Row topBottomMarginSlider(BookStyle bookStyle, StateSetter setState) {
      final txtColor = Theme.of(context).colorScheme.onSurface;

      return Row(
        children: [
          IconAndText(
            icon: Icon(AdaptiveIcons.verticalAlignTop),
            text: L10n.of(context).reading_page_top_margin,
          ),
          Expanded(
            child: SliderTheme(
              data: _getSliderTheme(
                txtColor,
                valueToString: (value) => (value / 20).toStringAsFixed(0),
              ),
              child: Slider(
                padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
                value: bookStyle.topMargin,
                onChanged: (double value) {
                  setState(() {
                    bookStyle.topMargin = value;
                    epubPlayerKey.currentState?.changeStyle(bookStyle);
                    Prefs().saveBookStyleToPrefs(bookStyle);
                  });
                },
                min: 0,
                max: 200,
                divisions: 10,
                label: (bookStyle.topMargin / 20).toStringAsFixed(0),
              ),
            ),
          ),
          IconAndText(
            icon: Icon(AdaptiveIcons.verticalAlignBottom),
            text: L10n.of(context).reading_page_bottom_margin,
          ),
          Expanded(
            child: SliderTheme(
              data: _getSliderTheme(
                txtColor,
                valueToString: (value) => (value / 20).toStringAsFixed(0),
              ),
              child: Slider(
                padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
                value: bookStyle.bottomMargin,
                onChanged: (double value) {
                  setState(() {
                    bookStyle.bottomMargin = value;
                    epubPlayerKey.currentState?.changeStyle(bookStyle);
                    Prefs().saveBookStyleToPrefs(bookStyle);
                  });
                },
                min: 0,
                max: 200,
                divisions: 10,
                label: (bookStyle.bottomMargin / 20).toStringAsFixed(0),
              ),
            ),
          ),
        ],
      );
    }

    Widget fontWeightSlider(BookStyle bookStyle, StateSetter setState) {
      final txtColor = Theme.of(context).colorScheme.onSurface;

      return Row(
        children: [
          IconAndText(
            icon: Icon(AdaptiveIcons.formatBold),
            text: L10n.of(context).reading_page_font_weight,
          ),
          Expanded(
            child: SliderTheme(
              data: _getSliderTheme(
                txtColor,
                valueToString: (value) => value.toStringAsFixed(0),
              ),
              child: Slider(
                padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
                value: bookStyle.fontWeight,
                onChanged: (double value) {
                  setState(() {
                    bookStyle.fontWeight = value;
                    epubPlayerKey.currentState?.changeStyle(bookStyle);
                    Prefs().saveBookStyleToPrefs(bookStyle);
                  });
                },
                min: 100,
                max: 900,
                divisions: 8,
                label: bookStyle.fontWeight.toString(),
              ),
            ),
          ),
        ],
      );
    }

    Widget paragraphSpacingSlider(BookStyle bookStyle, StateSetter setState) {
      final txtColor = Theme.of(context).colorScheme.onSurface;

      return Row(
        children: [
          IconAndText(
            icon: Icon(AdaptiveIcons.height),
            text: L10n.of(context).reading_page_paragraph_spacing,
          ),
          Expanded(
            child: SliderTheme(
              data: _getSliderTheme(
                txtColor,
                valueToString: (value) => (value / 5 * 10).round().toString(),
              ),
              child: Slider(
                padding: const EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
                value: bookStyle.paragraphSpacing,
                onChanged: (double value) {
                  setState(() {
                    bookStyle.paragraphSpacing = value;
                    epubPlayerKey.currentState?.changeStyle(bookStyle);
                    Prefs().saveBookStyleToPrefs(bookStyle);
                  });
                },
                min: 0,
                max: 5,
                divisions: 10,
                label: (bookStyle.paragraphSpacing / 5 * 10).round().toString(),
              ),
            ),
          ),
        ],
      );
    }

    Widget sliders() {
      BookStyle bookStyle = Prefs().bookStyle;
      return StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) => Column(
          children: [
            textIndent(bookStyle, setState),
            topBottomMarginSlider(bookStyle, setState),
            letterSpacingSlider(bookStyle, setState),
            fontWeightSlider(bookStyle, setState),
            paragraphSpacingSlider(bookStyle, setState),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(DesignSystem.spaceS),
      child: Column(
        children: [
          sliders(),
        ],
      ),
    );
  }
}
