import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/other_settings.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/reading_settings.dart';
import 'package:dasso_reader/widgets/reading_page/more_settings/style_settings.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:contentsize_tabbarview/contentsize_tabbarview.dart';
import 'package:flutter/material.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';

enum ReadingSettings { theme, style, reading }

void showMoreSettings(ReadingSettings settings) {
  BuildContext context = navigatorKey.currentContext!;
  // Navigator.of(context).pop();
  readingPageKey.currentState!.showOrHideAppBarAndBottomBar(false);

  // Get reading theme colors
  final readingTextColor = Color(int.parse('0x${Prefs().readTheme.textColor}'));
  final readingBackgroundColor =
      Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));

  List<Tab> tabs = [
    Tab(
      text: L10n.of(context).reading_page_reading,
      // Use reading text color for tabs
      icon: Icon(Icons.book, color: readingTextColor),
    ),
    Tab(
      text: L10n.of(context).reading_page_style,
      icon: Icon(Icons.style, color: readingTextColor),
    ),
    Tab(
      text: L10n.of(context).reading_page_other,
      icon: Icon(AdaptiveIcons.settings, color: readingTextColor),
    ),
  ];

  List<Widget> children = [
    const ReadingMoreSettings(),
    const StyleSettings(),
    const OtherSettings(),
  ];

  TabController? tabController = TabController(
    length: tabs.length,
    vsync: Navigator.of(context),
    initialIndex: settings == ReadingSettings.theme ? 0 : 1,
  );

  showDialog<void>(
    context: context,
    builder: (context) {
      return Theme(
        // Apply reading theme to dialog
        data: ThemeData.from(
          colorScheme: ColorScheme.light(
            primary: readingTextColor,
            onPrimary: readingBackgroundColor,
            secondary: readingTextColor,
            onSecondary: readingBackgroundColor,
            surface: readingBackgroundColor,
            onSurface: readingTextColor,
          ),
        ),
        child: Dialog(
          backgroundColor: readingBackgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusL),
          ),
          child: PointerInterceptor(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: DesignSystem.getAdaptiveDialogWidth(context),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Semantics(
                    label: 'Reading settings tabs',
                    hint: 'Navigate between reading, style, and other settings',
                    child: TabBar(
                      controller: tabController,
                      tabs: tabs.asMap().entries.map((entry) {
                        int index = entry.key;
                        Tab tab = entry.value;
                        String tabName = index == 0
                            ? 'Reading'
                            : index == 1
                                ? 'Style'
                                : 'Other';
                        return SemanticHelpers.navigationItem(
                          destination: '$tabName settings',
                          isSelected: tabController.index == index,
                          index: index,
                          total: tabs.length,
                          child: tab,
                        );
                      }).toList(),
                      indicatorColor: readingTextColor,
                      labelColor: readingTextColor,
                      unselectedLabelColor:
                          readingTextColor.withValues(alpha: 0.6),
                    ),
                  ),
                  Divider(
                    height: 0,
                    color: readingTextColor.withValues(alpha: 0.2),
                  ),
                  ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.of(context).size.height * 0.5,
                      maxWidth: MediaQuery.of(context).size.width * 0.8,
                    ),
                    child: DefaultTextStyle(
                      style: TextStyle(color: readingTextColor),
                      child: SingleChildScrollView(
                        child: ContentSizeTabBarView(
                          animationDuration: DesignSystem.durationExtraSlow,
                          controller: tabController,
                          children: children,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    },
  );
}
