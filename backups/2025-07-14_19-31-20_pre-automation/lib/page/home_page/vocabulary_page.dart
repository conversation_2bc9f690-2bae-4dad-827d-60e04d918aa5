import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';

class VocabularyPage extends StatelessWidget {
  const VocabularyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            AdaptiveIcons.menuBookRounded,
            size: DesignSystem.getAdjustedIconSize(80),
            color: colorScheme.primary.withValues(alpha: 0.8),
          ),
          DesignSystem.verticalSpaceL,
          Text(
            'Vocabulary',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
          ),
          DesignSystem.verticalSpaceM,
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceXL,
              vertical: DesignSystem.spaceS,
            ),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(DesignSystem.radiusL + 4),
            ),
            child: Text(
              'Coming Soon',
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
            ),
          ),
          DesignSystem.verticalSpaceXL,
          SizedBox(
            width: 300,
            child: Text(
              'Your personalized vocabulary collection and training tool is under development.',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
