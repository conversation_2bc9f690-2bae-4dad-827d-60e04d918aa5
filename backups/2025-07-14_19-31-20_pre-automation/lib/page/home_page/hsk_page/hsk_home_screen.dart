import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/page/home_page/hsk_page/hsk_set_details_screen.dart';
import 'package:dasso_reader/providers/hsk_providers.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/widgets/decorations/mountain_painter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class HskHomeScreen extends ConsumerWidget {
  const HskHomeScreen({super.key});

  // =====================================================
  // THEME-AWARE COMPONENTS - WCAG AAA Compliance
  // =====================================================

  /// Theme-aware gradient decoration with WCAG AAA compliance
  /// Replaces hardcoded colors with Material Design 3 theme colors
  BoxDecoration _getBackgroundGradient(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          colorScheme.primaryContainer,
          colorScheme.secondaryContainer,
        ],
      ),
    );
  }

  /// Theme-aware retry button style with WCAG AAA compliance
  ButtonStyle _getRetryButtonStyle(BuildContext context) =>
      ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.secondary,
        foregroundColor:
            DesignSystem.getSettingsTextColor(context, isPrimary: true),
        minimumSize: const Size(120, 44), // Minimum touch target
      );

  /// Theme-aware text styles with WCAG AAA compliance
  TextStyle _getEmptyStateTextStyle(BuildContext context) =>
      Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
          ) ??
      TextStyle(
        color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
      );

  TextStyle _getErrorTitleStyle(BuildContext context) =>
      Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
            fontWeight: FontWeight.bold,
          ) ??
      TextStyle(
        color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        fontWeight: FontWeight.bold,
      );

  TextStyle _getErrorSubtitleStyle(BuildContext context) =>
      Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
          ) ??
      TextStyle(
        color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
      );

  /// Calculates optimal cross-axis count based on screen size and content amount
  /// Now uses DesignSystem breakpoints and improved logic
  int _calculateCrossAxisCount(BuildContext context, int itemCount) {
    final orientation = MediaQuery.of(context).orientation;

    // Use DesignSystem breakpoints for consistency
    if (DesignSystem.isDesktop(context)) {
      // Desktop: 3-5 columns based on content density and orientation
      if (orientation == Orientation.landscape) {
        return itemCount > 15
            ? 5
            : itemCount > 10
                ? 4
                : 3;
      } else {
        return itemCount > 12 ? 4 : 3;
      }
    } else if (DesignSystem.isTablet(context)) {
      // Tablet: 2-4 columns based on orientation and content
      if (orientation == Orientation.landscape) {
        return itemCount > 12 ? 4 : 3;
      } else {
        return itemCount > 8 ? 3 : 2;
      }
    } else {
      // Mobile: 2-3 columns based on orientation and content
      if (orientation == Orientation.landscape) {
        return itemCount > 6 ? 3 : 2;
      } else {
        return itemCount > 8 ? 3 : 2;
      }
    }
  }

  /// Calculates optimal aspect ratio for better space utilization
  /// Now considers orientation and uses responsive logic
  double _calculateAspectRatio(
    BuildContext context,
    int itemCount,
    int crossAxisCount,
  ) {
    final orientation = MediaQuery.of(context).orientation;

    if (DesignSystem.isDesktop(context)) {
      // Desktop: more compact for better information density
      return orientation == Orientation.landscape
          ? (crossAxisCount >= 5 ? 1.0 : 1.2)
          : (crossAxisCount >= 4 ? 1.1 : 1.25);
    } else if (DesignSystem.isTablet(context)) {
      // Tablet: balanced approach
      return orientation == Orientation.landscape
          ? (crossAxisCount >= 4 ? 1.1 : 1.3)
          : (crossAxisCount >= 3 ? 1.2 : 1.35);
    } else {
      // Mobile: optimize for readability
      return orientation == Orientation.landscape
          ? (crossAxisCount >= 3 ? 1.0 : 1.2)
          : (crossAxisCount >= 3 ? 1.15 : 1.3);
    }
  }

  /// Builds the adaptive grid with enhanced responsive design - optimized for performance
  Widget _buildAdaptiveGrid(
    BuildContext context,
    List<HskCharacterSet> characterSets,
  ) {
    final itemCount = characterSets.length;
    final crossAxisCount = _calculateCrossAxisCount(context, itemCount);
    final aspectRatio =
        _calculateAspectRatio(context, itemCount, crossAxisCount);

    // Calculate responsive spacing - optimized for mobile content density
    final spacing = DesignSystem.isDesktop(context)
        ? DesignSystem.spaceL
        : DesignSystem.isTablet(context)
            ? DesignSystem.spaceM
            : DesignSystem.spaceS *
                0.75; // Slightly tighter on mobile for more content

    return AnimatedSwitcher(
      duration:
          DesignSystem.durationFast, // Faster transition for better performance
      child: GridView.builder(
        key: ValueKey('grid_${crossAxisCount}_${characterSets.length}'),
        physics: const AlwaysScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
          childAspectRatio: aspectRatio,
        ),
        itemCount: itemCount,
        itemBuilder: (context, index) {
          final set = characterSets[index];
          return CharacterSetButton(
            characterSet: set,
          );
        },
      ),
    );
  }

  /// Builds responsive mountain decoration that adapts to content density
  /// Now uses optimized MountainDecoration widget for consistency
  Widget _buildResponsiveMountain(BuildContext context, int itemCount) {
    final screenHeight = ResponsiveSystem.getScreenHeight(context);
    final orientation = ResponsiveSystem.getOrientation(context);

    // Calculate responsive mountain height
    double baseHeight;
    if (DesignSystem.isDesktop(context)) {
      baseHeight = orientation == Orientation.landscape ? 100.0 : 120.0;
    } else if (DesignSystem.isTablet(context)) {
      baseHeight = orientation == Orientation.landscape ? 80.0 : 100.0;
    } else {
      baseHeight = orientation == Orientation.landscape ? 60.0 : 80.0;
    }

    // Reduce mountain height for longer lists to maximize content space
    if (itemCount > 15) {
      baseHeight *= 0.7;
    } else if (itemCount > 10) {
      baseHeight *= 0.8;
    } else if (itemCount > 8) {
      baseHeight *= 0.9;
    }

    // Ensure minimum height and respect screen constraints
    baseHeight = baseHeight.clamp(60.0, screenHeight * 0.15);

    return Container(
      height: baseHeight,
      width: double.infinity,
      alignment: Alignment.bottomCenter,
      child: MountainDecoration(height: baseHeight - 20),
    );
  }

  /// Gets responsive title font size - optimized for mobile hierarchy
  double _getTitleFontSize(BuildContext context) {
    final textScaler = MediaQuery.textScalerOf(context);
    final orientation = MediaQuery.of(context).orientation;
    double baseFontSize;

    if (DesignSystem.isDesktop(context)) {
      baseFontSize = 28.0;
    } else if (DesignSystem.isTablet(context)) {
      baseFontSize = orientation == Orientation.landscape ? 24.0 : 22.0;
    } else {
      // Mobile: Reduced size to not compete with content
      baseFontSize = orientation == Orientation.landscape ? 20.0 : 18.0;
    }

    // Apply text scaling but clamp to reasonable limits
    return (baseFontSize * textScaler.scale(baseFontSize) / baseFontSize)
        .clamp(16.0, 32.0);
  }

  /// Gets responsive subtitle font size for brand context
  double _getSubtitleFontSize(BuildContext context) {
    final titleSize = _getTitleFontSize(context);
    return (titleSize * 0.45).clamp(10.0, 14.0);
  }

  /// Gets responsive level selector font size
  double _getLevelSelectorFontSize(BuildContext context) {
    final textScaler = MediaQuery.textScalerOf(context);
    double baseFontSize;

    if (DesignSystem.isDesktop(context)) {
      baseFontSize = 20.0;
    } else if (DesignSystem.isTablet(context)) {
      baseFontSize = 19.0;
    } else {
      baseFontSize = 18.0;
    }

    return (baseFontSize * textScaler.scale(baseFontSize) / baseFontSize)
        .clamp(16.0, 24.0);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the current HSK level
    final currentLevel = ref.watch(currentHskLevelProvider);

    // Watch available HSK levels
    final hskLevelsAsync = ref.watch(hskLevelsProvider);

    // Watch character sets for the current level
    final characterSetsAsync = ref.watch(hskCharacterSetsProvider);

    return Scaffold(
      body: Container(
        decoration: _getBackgroundGradient(context),
        child: SafeArea(
          child: Column(
            children: [
              // Optimized spacing for mobile - less generous on small screens
              SizedBox(
                height: DesignSystem.isDesktop(context)
                    ? DesignSystem.spaceM
                    : DesignSystem.spaceS,
              ),

              // App title with subtitle for better brand identity and reduced visual weight
              Padding(
                padding: DesignSystem.getAdaptivePadding(context),
                child: Column(
                  children: [
                    Text(
                      'CHINESE IN FLOW',
                      style: Theme.of(context).textTheme.displaySmall?.copyWith(
                        fontSize: _getTitleFontSize(context),
                        fontWeight: FontWeight.bold,
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: true,
                        ),
                        letterSpacing:
                            DesignSystem.isDesktop(context) ? 1.2 : 0.8,
                        shadows: [
                          Shadow(
                            offset: const Offset(1, 1),
                            blurRadius:
                                DesignSystem.isDesktop(context) ? 3.0 : 2.0,
                            color: Theme.of(context)
                                .colorScheme
                                .shadow
                                .withValues(alpha: 0.6),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                      semanticsLabel: 'Chinese in Flow - HSK Learning App',
                    ),

                    // Subtitle for better brand context and hierarchy
                    const SizedBox(height: DesignSystem.spaceXS / 2),
                    Text(
                      'HSK Character Learning',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontSize: _getSubtitleFontSize(context),
                            fontWeight: FontWeight.w400,
                            color: DesignSystem.getSettingsTextColor(
                              context,
                              isPrimary: false,
                            ),
                            letterSpacing: 0.5,
                          ),
                      textAlign: TextAlign.center,
                      semanticsLabel: 'HSK Character Learning Platform',
                    ),
                  ],
                ),
              ),

              // Responsive spacing - more compact on mobile
              SizedBox(
                height: DesignSystem.isDesktop(context)
                    ? DesignSystem.spaceL
                    : DesignSystem.isTablet(context)
                        ? DesignSystem.spaceM
                        : DesignSystem.spaceS,
              ),

              // HSK Level Selector with improved accessibility and responsive design
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal:
                      DesignSystem.getAdaptivePadding(context).horizontal / 2,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Previous level button with minimum touch target
                    SizedBox(
                      width: DesignSystem
                          .widgetMinTouchTarget, // Minimum touch target
                      height: DesignSystem.widgetMinTouchTarget,
                      child: IconButton(
                        onPressed: () {
                          hskLevelsAsync.whenData((levels) {
                            final currentIndex = levels.indexOf(currentLevel);
                            if (currentIndex > 0) {
                              ref
                                  .read(currentHskLevelProvider.notifier)
                                  .setLevel(
                                    levels[currentIndex - 1],
                                  );
                            }
                          });
                        },
                        icon: Icon(
                          AdaptiveIcons.arrowBackIos,
                          color: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: true,
                          ),
                        ),
                        tooltip: 'Previous HSK Level',
                        splashRadius: 22.0,
                      ),
                    ),

                    const SizedBox(width: DesignSystem.spaceM),

                    // Level display container with responsive design
                    Container(
                      constraints: BoxConstraints(
                        minHeight: DesignSystem
                            .widgetMinTouchTarget, // Minimum touch target height
                        minWidth:
                            DesignSystem.isDesktop(context) ? 120.0 : 100.0,
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: DesignSystem.spaceM,
                        vertical: DesignSystem.spaceS,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .surfaceContainerHighest
                            .withValues(alpha: 0.8),
                        borderRadius:
                            BorderRadius.circular(DesignSystem.radiusM),
                      ),
                      child: Center(
                        child: Text(
                          currentLevel,
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(
                                fontSize: _getLevelSelectorFontSize(context),
                                fontWeight: FontWeight.bold,
                                color: DesignSystem.getSettingsTextColor(
                                  context,
                                  isPrimary: true,
                                ),
                              ),
                          semanticsLabel: 'Current HSK Level: $currentLevel',
                        ),
                      ),
                    ),

                    const SizedBox(width: DesignSystem.spaceM),

                    // Next level button with minimum touch target
                    SizedBox(
                      width: DesignSystem
                          .widgetMinTouchTarget, // Minimum touch target
                      height: DesignSystem.widgetMinTouchTarget,
                      child: IconButton(
                        onPressed: () {
                          hskLevelsAsync.whenData((levels) {
                            final currentIndex = levels.indexOf(currentLevel);
                            if (currentIndex < levels.length - 1) {
                              ref
                                  .read(currentHskLevelProvider.notifier)
                                  .setLevel(
                                    levels[currentIndex + 1],
                                  );
                            }
                          });
                        },
                        icon: Icon(
                          AdaptiveIcons.arrowForwardIos,
                          color: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: true,
                          ),
                        ),
                        tooltip: 'Next HSK Level',
                        splashRadius: 22.0,
                      ),
                    ),
                  ],
                ),
              ),

              // Optimized spacing before grid - more compact on mobile
              SizedBox(
                height: DesignSystem.isDesktop(context)
                    ? DesignSystem.spaceL
                    : DesignSystem.isTablet(context)
                        ? DesignSystem.spaceM
                        : DesignSystem.spaceXS,
              ),

              // Character Set Grid - Enhanced Responsive Design with optimized mobile spacing
              Expanded(
                child: characterSetsAsync.when(
                  data: (characterSets) {
                    if (characterSets.isEmpty) {
                      return Center(
                        child: Padding(
                          padding: DesignSystem.getAdaptivePadding(context),
                          child: Text(
                            'No character sets available for this level',
                            style: _getEmptyStateTextStyle(context).copyWith(
                              fontSize: _getLevelSelectorFontSize(context),
                            ),
                            textAlign: TextAlign.center,
                            semanticsLabel:
                                'No character sets available for the current HSK level',
                          ),
                        ),
                      );
                    }

                    return Padding(
                      padding: DesignSystem.getAdaptivePadding(context),
                      child: _buildAdaptiveGrid(context, characterSets),
                    );
                  },
                  loading: () => Center(
                    child: CircularProgressIndicator(
                      color: DesignSystem.getSettingsTextColor(
                        context,
                        isPrimary: true,
                      ),
                      semanticsLabel: 'Loading character sets',
                    ),
                  ),
                  error: (error, stack) => Center(
                    child: Padding(
                      padding: DesignSystem.getAdaptivePadding(context),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            AdaptiveIcons.errorOutline,
                            color: DesignSystem.getSettingsTextColor(
                              context,
                              isPrimary: false,
                            ),
                            size: DesignSystem.widgetIconSizeLarge,
                          ),
                          const SizedBox(height: DesignSystem.spaceM),
                          Text(
                            'Error loading character sets',
                            style: _getErrorTitleStyle(context).copyWith(
                              fontSize: _getLevelSelectorFontSize(context),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: DesignSystem.spaceS),
                          Text(
                            'Please check your connection and try again',
                            style: _getErrorSubtitleStyle(context).copyWith(
                              fontSize: _getLevelSelectorFontSize(context) - 2,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: DesignSystem.spaceL),
                          ElevatedButton.icon(
                            onPressed: () {
                              ref.invalidate(hskCharacterSetsProvider);
                            },
                            icon: Icon(AdaptiveIcons.refresh),
                            label: const Text('Retry'),
                            style: _getRetryButtonStyle(context),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Responsive mountain silhouette decoration
              characterSetsAsync.when(
                data: (characterSets) =>
                    _buildResponsiveMountain(context, characterSets.length),
                loading: () =>
                    _buildResponsiveMountain(context, 6), // Default size
                error: (_, __) =>
                    _buildResponsiveMountain(context, 6), // Default size
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CharacterSetButton extends ConsumerStatefulWidget {
  final HskCharacterSet characterSet;

  const CharacterSetButton({
    super.key,
    required this.characterSet,
  });

  @override
  ConsumerState<CharacterSetButton> createState() => _CharacterSetButtonState();
}

class _CharacterSetButtonState extends ConsumerState<CharacterSetButton> {
  bool _isHovered = false;

  // =====================================================
  // THEME-AWARE COMPONENTS - WCAG AAA Compliance
  // =====================================================

  /// Theme-aware button colors with WCAG AAA compliance
  Color _getNormalBackgroundColor(BuildContext context) =>
      Theme.of(context).colorScheme.surfaceContainerHigh;
  Color _getHoveredBackgroundColor(BuildContext context) =>
      Theme.of(context).colorScheme.surfaceContainerHighest;
  Color _getLabelColor(BuildContext context) =>
      DesignSystem.getSettingsTextColor(context, isPrimary: false);
  Color _getTextColor(BuildContext context) =>
      DesignSystem.getSettingsTextColor(context, isPrimary: true);

  /// Theme-aware shadow color
  Color _getShadowColor(BuildContext context) =>
      Theme.of(context).colorScheme.shadow.withValues(alpha: 0.3);

  /// Gets responsive button text sizes based on screen size
  double _getLabelFontSize(BuildContext context) {
    final textScaler = MediaQuery.textScalerOf(context);
    double baseFontSize;

    if (DesignSystem.isDesktop(context)) {
      baseFontSize = 13.0;
    } else if (DesignSystem.isTablet(context)) {
      baseFontSize = 12.5;
    } else {
      baseFontSize = 12.0;
    }

    return (baseFontSize * textScaler.scale(baseFontSize) / baseFontSize)
        .clamp(10.0, 16.0);
  }

  double _getIdFontSize(BuildContext context) {
    final textScaler = MediaQuery.textScalerOf(context);
    double baseFontSize;

    if (DesignSystem.isDesktop(context)) {
      baseFontSize = 17.0;
    } else if (DesignSystem.isTablet(context)) {
      baseFontSize = 16.5;
    } else {
      baseFontSize = 16.0;
    }

    return (baseFontSize * textScaler.scale(baseFontSize) / baseFontSize)
        .clamp(14.0, 20.0);
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedScale(
        scale: _isHovered ? 1.02 : 1.0, // Subtle hover effect for performance
        duration: DesignSystem.durationFast,
        curve: Curves.easeOutCubic,
        child: ElevatedButton(
          onPressed: () {
            // Add haptic feedback for better UX
            HapticFeedback.lightImpact();

            // Navigate to set details screen with optimized transition
            Navigator.push(
              context,
              PageRouteBuilder<void>(
                pageBuilder: (context, animation, secondaryAnimation) =>
                    HskSetDetailsScreen(characterSet: widget.characterSet),
                transitionsBuilder:
                    (context, animation, secondaryAnimation, child) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).animate(
                      CurvedAnimation(
                        parent: animation,
                        curve: Curves.easeOutCubic,
                      ),
                    ),
                    child: child,
                  );
                },
                transitionDuration: DesignSystem.durationMedium,
              ),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: _isHovered
                ? _getHoveredBackgroundColor(context)
                : _getNormalBackgroundColor(context),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(DesignSystem.radiusM),
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceM,
              vertical: DesignSystem.spaceS,
            ),
            minimumSize: const Size(80, 44), // Ensure minimum touch target
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            elevation:
                _isHovered ? DesignSystem.elevationM : DesignSystem.elevationS,
            shadowColor: _getShadowColor(context),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  'Characters',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: _getLabelColor(context),
                        fontSize: _getLabelFontSize(context),
                      ),
                  maxLines: 1,
                  semanticsLabel: 'Character set',
                ),
              ),
              const SizedBox(height: DesignSystem.spaceXS / 2),
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                  '${widget.characterSet.startId}-${widget.characterSet.endId}',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: _getTextColor(context),
                        fontSize: _getIdFontSize(context),
                        fontWeight: FontWeight.bold,
                      ),
                  maxLines: 1,
                  semanticsLabel:
                      'Characters ${widget.characterSet.startId} to ${widget.characterSet.endId}',
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
