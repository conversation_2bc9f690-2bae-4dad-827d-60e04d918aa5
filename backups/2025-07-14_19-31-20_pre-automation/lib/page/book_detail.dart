import 'dart:io';

import 'package:dasso_reader/dao/book.dart';
import 'package:dasso_reader/dao/reading_time.dart';
import 'package:dasso_reader/enums/sync_direction.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/reading_time.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/service/book.dart';
import 'package:dasso_reader/utils/date/convert_seconds.dart';
import 'package:dasso_reader/utils/get_path/get_base_path.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/widgets/bookshelf/book_cover.dart';
import 'package:dasso_reader/widgets/highlight_digit.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:path/path.dart' as path;

class BookDetail extends ConsumerStatefulWidget {
  const BookDetail({super.key, required this.book});

  final Book book;

  @override
  ConsumerState<BookDetail> createState() => _BookDetailState();
}

class _BookDetailState extends ConsumerState<BookDetail> {
  late double rating;
  bool isEditing = false;
  late Book _book;
  bool _isCollapsed = false;

  @override
  void initState() {
    super.initState();
    rating = widget.book.rating;
    _book = widget.book;
  }

  @override
  Widget build(BuildContext context) {
    Widget buildBackground() {
      return Container(
        color: Theme.of(context).colorScheme.surface,
        child: ShaderMask(
          shaderCallback: (rect) {
            return LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Theme.of(context).colorScheme.surface.withValues(alpha: 0.20),
                Colors.transparent,
              ],
            ).createShader(
              Rect.fromLTRB(0, 0, rect.width, rect.height),
            );
          },
          blendMode: BlendMode.dstIn,
          child: bookCover(
            context,
            _book,
            height: 600,
            width: ResponsiveSystem.getScreenWidth(context),
          ),
        ),
      );
    }

    Widget buildBookBaseDetail(double width) {
      TextStyle bookTitleStyle = TextStyle(
        fontSize: DesignSystem.getAdjustedFontSize(
          context,
          DesignSystem.fontSizeHeadingM,
        ),
        fontFamily: 'NotoSansSC',
        fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
        color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
      );
      TextStyle bookAuthorStyle = TextStyle(
        fontSize: DesignSystem.getAdjustedFontSize(
          context,
          DesignSystem.fontSizeM + 1, // 15.0 equivalent (14.0 + 1.0)
        ),
        fontFamily: 'NotoSansSC',
        color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
      );
      double top = 60;

      return SizedBox(
        height: 270 + top,
        child: Stack(
          children: [
            // background card
            Positioned(
              left: 0,
              top: 150 + top,
              child: SizedBox(
                height: 120,
                width: width,
                child: Card(
                  child: Row(
                    children: [
                      const Spacer(),
                      // progress ring
                      Stack(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(
                              DesignSystem.spaceL -
                                  DesignSystem.spaceXS, // 20.0
                            ),
                            width: 100,
                            height: 100,
                            child: CircularProgressIndicator(
                              value: widget.book.readingPercentage,
                              strokeWidth: 6,
                              backgroundColor: Colors.grey[400],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ),
                          Positioned.fill(
                            child: Center(
                              child: Text(
                                '${(widget.book.readingPercentage * 100).toStringAsFixed(0)}%',
                                style: TextStyle(
                                  fontSize: DesignSystem.fontSizeL,
                                  fontWeight:
                                      DesignSystem.getAdjustedFontWeight(
                                    FontWeight.bold,
                                  ),
                                  color: DesignSystem.getSettingsTextColor(
                                    context,
                                    isPrimary: true,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // book cover
            Positioned(
              left: 20,
              top: 0 + top,
              child: GestureDetector(
                onTap: () async {
                  if (!isEditing) {
                    return;
                  }

                  FilePickerResult? result =
                      await FilePicker.platform.pickFiles(
                    type: FileType.image,
                    allowMultiple: false,
                  );

                  if (result == null) {
                    return;
                  }

                  File image = File(result.files.single.path!);

                  AnxLog.info('BookDetail: Image path: ${image.path}');
                  // Delete the existing cover image file
                  final File oldCoverImageFile =
                      File(widget.book.coverFullPath);
                  if (await oldCoverImageFile.exists()) {
                    await oldCoverImageFile.delete();
                  }

                  // Extract directory path from cover path using cross-platform path handling
                  final directoryPath = path.dirname(widget.book.coverPath);
                  final sanitizedTitle = (widget.book.title.length > 20
                          ? widget.book.title.substring(0, 20)
                          : widget.book.title)
                      .replaceAll(RegExp(r'[<>:"/\\|?*]'), '_')
                      .replaceAll('\n', '')
                      .replaceAll('\r', '')
                      .trim();

                  String newPath = path.join(
                    directoryPath,
                    '$sanitizedTitle-${DateTime.now().millisecond.toString()}.png',
                  );

                  AnxLog.info('BookDetail: New path: $newPath');
                  String newFullPath = getBasePath(newPath);

                  final File newCoverImageFile = File(newFullPath);
                  await newCoverImageFile
                      .writeAsBytes(await image.readAsBytes());
                  widget.book.coverPath = newPath;

                  setState(() {
                    widget.book.coverPath = newPath;
                    updateBook(widget.book);
                    AnxWebdav().syncData(SyncDirection.upload, ref);
                    ref.read(bookListProvider.notifier).refresh();
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius:
                        BorderRadius.circular(DesignSystem.radiusS + 6),
                    boxShadow: [
                      // Set the shadow
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.5),
                        spreadRadius: 6,
                        blurRadius: 30,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Hero(
                    tag: widget.book.coverFullPath,
                    child: bookCover(
                      context,
                      widget.book,
                      height: 230,
                      width: 160,
                    ),
                  ),
                ),
              ),
            ),
            // rating bar
            Positioned(
              left: 30,
              top: 240 + top,
              child: RatingBar.builder(
                initialRating: rating,
                minRating: 0,
                direction: Axis.horizontal,
                allowHalfRating: true,
                itemCount: 5,
                itemSize: 20,
                itemPadding: const EdgeInsets.symmetric(
                  horizontal: DesignSystem.spaceXS, // 4.0
                ),
                itemBuilder: (context, _) => Icon(
                  AdaptiveIcons.star,
                  color: Colors.amber,
                ),
                onRatingUpdate: (rating) {
                  setState(() {
                    this.rating = rating;
                    updateBookRating(widget.book, rating);
                  });
                },
              ),
            ),
            // book title and author
            Positioned(
              left: 190,
              top: 5 + top,
              child: SizedBox(
                width: width - 190,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      autofocus: true,
                      initialValue: widget.book.title,
                      enabled: isEditing,
                      style: bookTitleStyle,
                      maxLines: null,
                      minLines: 1,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        isCollapsed: true,
                      ),
                      onChanged: (value) {
                        widget.book.title = value.replaceAll('\n', ' ');
                      },
                    ),
                    const SizedBox(
                      height: DesignSystem.spaceXS + 1, // 5.0
                    ),
                    TextFormField(
                      initialValue: widget.book.author,
                      enabled: isEditing,
                      style: bookAuthorStyle,
                      maxLines: null,
                      minLines: 1,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        isCollapsed: true,
                      ),
                      onChanged: (value) {
                        widget.book.author = value;
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    Widget buildEditButton() {
      return Row(
        children: [
          const Spacer(),
          isEditing
              ? ElevatedButton(
                  child: Row(
                    children: [
                      Icon(AdaptiveIcons.save),
                      const SizedBox(
                        width: DesignSystem.spaceXS + 1, // 5.0
                      ),
                      Text(L10n.of(context).book_detail_save),
                    ],
                  ),
                  onPressed: () {
                    setState(() {
                      isEditing = false;
                      updateBook(widget.book);
                      AnxWebdav().syncData(SyncDirection.upload, ref);
                      ref.read(bookListProvider.notifier).refresh();
                    });
                  },
                )
              : ElevatedButton(
                  child: Row(
                    children: [
                      Icon(AdaptiveIcons.edit),
                      const SizedBox(
                        width: DesignSystem.spaceXS + 1, // 5.0
                      ),
                      Text(L10n.of(context).book_detail_edit),
                    ],
                  ),
                  onPressed: () {
                    setState(() {
                      isEditing = true;
                    });
                  },
                ),
        ],
      );
    }

    Widget buildBookStatistics() {
      Widget buildNthBooksItem() {
        TextStyle textStyle = TextStyle(
          fontSize: DesignSystem.fontSizeM,
          color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
        );
        TextStyle digitStyle = TextStyle(
          fontSize: DesignSystem.fontSizeXXL,
          fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        );

        return Padding(
          padding: const EdgeInsets.only(
            left: DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
            right: DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
          ),
          child: highlightDigit(
            context,
            L10n.of(context).book_detail_nth_book(widget.book.id),
            textStyle,
            digitStyle,
          ),
        );
      }

      Widget buildRankItem() {
        return Padding(
          padding: const EdgeInsets.only(
            left: DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
            right: DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
          ),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: rating.toString(),
                  style: TextStyle(
                    fontSize: DesignSystem.fontSizeXXL,
                    fontWeight:
                        DesignSystem.getAdjustedFontWeight(FontWeight.bold),
                    color: DesignSystem.getSettingsTextColor(
                      context,
                      isPrimary: true,
                    ),
                  ),
                ),
                TextSpan(
                  text: ' / 5',
                  style: TextStyle(
                    fontSize: DesignSystem.fontSizeM,
                    color: DesignSystem.getSettingsTextColor(
                      context,
                      isPrimary: false,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }

      Widget buildReadingTimeItem() {
        TextStyle digitStyle = TextStyle(
          fontSize: DesignSystem.fontSizeXXL,
          fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
        );
        TextStyle textStyle = TextStyle(
          fontSize: DesignSystem.fontSizeM,
          color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
        );
        return FutureBuilder<int>(
          future: selectTotalReadingTimeByBookId(widget.book.id),
          builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
            if (snapshot.hasData) {
              int totalReadingTime = snapshot.data!;
              int hours = totalReadingTime ~/ 3600;
              int minutes = totalReadingTime % 3600 ~/ 60;
              return Padding(
                padding: const EdgeInsets.only(
                  left: DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
                  right: DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
                ),
                child: Row(
                  children: [
                    highlightDigit(
                      context,
                      L10n.of(context).common_hours(hours),
                      textStyle,
                      digitStyle,
                    ),
                    highlightDigit(
                      context,
                      L10n.of(context).common_minutes(minutes),
                      textStyle,
                      digitStyle,
                    ),
                  ],
                ),
              );
            } else {
              return const Padding(
                padding: EdgeInsets.only(
                  left: DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
                  right: DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
                ),
                child: CircularProgressIndicator(),
              );
            }
          },
        );
      }

      VerticalDivider verticalDivider = const VerticalDivider(
        color: Colors.black12,
        thickness: 1,
        indent: 15,
        endIndent: 15,
      );

      return SizedBox(
        height: 130,
        width: ResponsiveSystem.getScreenWidth(context),
        child: Card(
          child: ListView(
            padding: const EdgeInsets.all(
              DesignSystem.spaceS + DesignSystem.spaceXS, // 10.0 (8 + 2)
            ),
            scrollDirection: Axis.horizontal,
            children: [
              Row(
                children: [
                  buildNthBooksItem(),
                  verticalDivider,
                  buildRankItem(),
                  verticalDivider,
                  buildReadingTimeItem(),
                ],
              ),
            ],
          ),
        ),
      );
    }

    Widget buildMoreDetail() {
      Widget buildReadingDetail() {
        return FutureBuilder<List<ReadingTime>>(
          future: selectReadingTimeByBookId(widget.book.id),
          builder: (
            BuildContext context,
            AsyncSnapshot<List<ReadingTime>> snapshot,
          ) {
            if (snapshot.hasData) {
              List<ReadingTime> readingTimes = snapshot.data!;
              return Column(
                children: List.generate(readingTimes.length, (index) {
                  int totalReadingTime = readingTimes[index].readingTime;
                  return Row(
                    children: [
                      Text(
                        readingTimes[index].date!,
                        style: TextStyle(
                          fontSize: DesignSystem.fontSizeM,
                          color: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: false,
                          ),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        convertSeconds(totalReadingTime),
                        style: TextStyle(
                          fontSize: DesignSystem.fontSizeM,
                          color: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: true,
                          ),
                        ),
                      ),
                    ],
                  );
                }),
              );
            } else {
              return const CircularProgressIndicator();
            }
          },
        );
      }

      TextStyle textStyle = TextStyle(
        fontSize: DesignSystem.getAdjustedFontSize(
          context,
          DesignSystem.fontSizeM + 1, // 15.0 equivalent (14.0 + 1.0)
        ),
        fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
        color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
      );
      return SingleChildScrollView(
        child: SizedBox(
          // height: 500,
          width: ResponsiveSystem.getScreenWidth(context),
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(
                DesignSystem.spaceM - 1, // 15.0 (16 - 1)
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${L10n.of(context).book_detail_import_date}${widget.book.createTime.toString().substring(0, 10)}',
                    style: textStyle,
                  ),
                  Text(
                    '${L10n.of(context).book_detail_last_read_date}${widget.book.updateTime.toString().substring(0, 10)}',
                    style: textStyle,
                  ),
                  const Divider(),
                  SizedBox(
                    // height: 200,
                    child: buildReadingDetail(),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return Stack(
      children: [
        Positioned.fill(child: buildBackground()),
        Scaffold(
          backgroundColor: Colors.transparent,
          body: NotificationListener<ScrollNotification>(
            onNotification: (ScrollNotification notification) {
              if (notification is ScrollUpdateNotification) {
                setState(() {
                  _isCollapsed = notification.metrics.pixels > 0;
                });
              }
              return false;
            },
            child: CustomScrollView(
              slivers: [
                SliverAppBar(
                  expandedHeight: 0,
                  pinned: true,
                  stretch: true,
                  backgroundColor: _isCollapsed
                      ? Theme.of(context)
                          .colorScheme
                          .surface
                          .withValues(alpha: 0.8)
                      : Colors.transparent,
                  flexibleSpace: FlexibleSpaceBar(
                    title: AnimatedOpacity(
                      opacity: _isCollapsed ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 300),
                      child: Text(
                        widget.book.title,
                        style:
                            const TextStyle(fontSize: DesignSystem.fontSizeM),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    centerTitle: true,
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(
                      DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
                      0,
                      DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
                      DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
                    ),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        if (DesignSystem.isTablet(context)) {
                          return Row(
                            children: [
                              Expanded(
                                flex: 1,
                                child: Column(
                                  children: [
                                    buildBookBaseDetail(
                                      constraints.maxWidth / 2 -
                                          (DesignSystem.spaceL -
                                              DesignSystem.spaceXS), // 20.0
                                    ),
                                    buildEditButton(),
                                    const SizedBox(
                                      height: DesignSystem.spaceXS + 1, // 5.0
                                    ),
                                    buildBookStatistics(),
                                  ],
                                ),
                              ),
                              const SizedBox(
                                width: DesignSystem.spaceL -
                                    DesignSystem.spaceXS, // 20.0
                              ),
                              Expanded(
                                flex: 1,
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    top: DesignSystem.spaceL -
                                        DesignSystem.spaceXS, // 20.0
                                    bottom: DesignSystem.spaceL -
                                        DesignSystem.spaceXS, // 20.0
                                  ),
                                  child: buildMoreDetail(),
                                ),
                              ),
                            ],
                          );
                        } else {
                          return Column(
                            children: [
                              buildBookBaseDetail(constraints.maxWidth),
                              buildEditButton(),
                              const SizedBox(
                                height: DesignSystem.spaceXS + 1, // 5.0
                              ),
                              buildBookStatistics(),
                              const SizedBox(
                                height: DesignSystem.spaceM - 1, // 15.0
                              ),
                              buildMoreDetail(),
                            ],
                          );
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
