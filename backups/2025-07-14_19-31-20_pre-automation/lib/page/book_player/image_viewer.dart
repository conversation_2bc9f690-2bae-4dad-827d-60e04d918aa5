import 'dart:convert';
import 'dart:typed_data';

import 'package:dasso_reader/utils/save_img.dart';
import 'package:dasso_reader/utils/get_path/get_temp_dir.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/save_image_to_path.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:photo_view/photo_view.dart';
import 'package:share_plus/share_plus.dart';

class ImageViewer extends StatelessWidget {
  final String image;
  final String bookName;

  const ImageViewer({
    super.key,
    required this.image,
    required this.bookName,
  });

  @override
  Widget build(BuildContext context) {
    Uint8List? imageBytes;
    String? imgType;

    try {
      final List<String> parts = image.split(',');

      // Validate format
      if (parts.length < 2) {
        AnxLog.severe(
          'Error decoding image: Invalid base64 format, expected "data:type;base64,data"',
        );
        return const Center(child: Text('Invalid image format'));
      }

      String base64 = parts[1];
      imageBytes = base64Decode(base64);

      // Safely extract image type
      final headerParts = parts[0].split('/');
      if (headerParts.length < 2) {
        AnxLog.severe('Error decoding image: Invalid header format');
        imgType = 'png'; // fallback
      } else {
        final extensionParts = headerParts[1].split(';');
        imgType = extensionParts.isNotEmpty ? extensionParts[0] : 'png';
      }
    } catch (e) {
      AnxLog.severe('Error decoding image: $e');
      return const Center(child: Text('Error decoding image'));
    }

    return Stack(
      children: [
        PhotoView(
          imageProvider: MemoryImage(imageBytes),
          backgroundDecoration: const BoxDecoration(color: Colors.black),
          loadingBuilder: (context, event) => const Center(
            child: CircularProgressIndicator(),
          ),
          minScale: PhotoViewComputedScale.contained * 0.8,
          maxScale: PhotoViewComputedScale.covered * 3,
        ),
        Positioned.fill(
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(
                18.0, // DesignSystem.spaceM + DesignSystem.spaceXS (16 + 2)
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(AdaptiveIcons.close, color: Colors.white),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        onPressed: () {
                          SaveImg.downloadImg(imageBytes!, imgType!, bookName);
                        },
                        icon: Icon(
                          AdaptiveIcons.downloadReading,
                          color: Colors.white,
                        ),
                      ),
                      IconButton(
                        onPressed: () async {
                          final path = await saveB64ImageToPath(
                            image,
                            (await getAnxTempDir()).path,
                            'AnxReader_$bookName',
                          );

                          Share.shareXFiles([XFile(path)]);
                        },
                        icon: Icon(AdaptiveIcons.share, color: Colors.white),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
