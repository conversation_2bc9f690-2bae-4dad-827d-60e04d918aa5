import 'dart:io';
import 'dart:math';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/providers/storage_info.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/widgets/delete_confirm.dart';
import 'package:dasso_reader/widgets/settings/settings_section.dart';
import 'package:dasso_reader/widgets/settings/settings_tile.dart';
import 'package:dasso_reader/widgets/settings/settings_title.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class StorageSettings extends ConsumerStatefulWidget {
  const StorageSettings({super.key});

  @override
  ConsumerState<StorageSettings> createState() => _StorageSettingsState();
}

class _StorageSettingsState extends ConsumerState<StorageSettings>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final storageInfoAsync = ref.watch(storageInfoProvider);

    Widget fileSizeTriling(String? size) {
      if (size == null) {
        return const CircularProgressIndicator.adaptive();
      }
      return Text(
        size,
        style: TextStyle(
          color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
          fontSize: Theme.of(context).textTheme.bodyMedium?.fontSize,
          fontWeight: Theme.of(context).textTheme.bodyMedium?.fontWeight,
        ),
      );
    }

    Widget cacheSizeTriling(String? size) {
      if (size == null) {
        return const CircularProgressIndicator.adaptive();
      }
      return SemanticHelpers.button(
        context: context,
        child: ElevatedButton(
          onPressed: () async {
            await ref.read(storageInfoProvider.notifier).clearCache();
            ref.invalidate(storageInfoProvider);
          },
          child: Text(
            '${L10n.of(context).storage_clear_cache} $size',
            style: TextStyle(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
            ),
          ),
        ),
        label: '${L10n.of(context).storage_clear_cache} $size',
        hint: 'Clear cached files to free up storage space',
        onTap: () async {
          await ref.read(storageInfoProvider.notifier).clearCache();
          ref.invalidate(storageInfoProvider);
        },
      );
    }

    return settingsSections(
      sections: [
        // Dictionary Preloading Section
        const SettingsSection(
          title: Text('Dictionary Preloading'),
          tiles: [
            CustomSettingsTile(
              child: DictionaryPreloadingTile(),
            ),
          ],
        ),

        SettingsSection(
          title: Text(L10n.of(context).storage_info),
          tiles: [
            CustomSettingsTile(
              child: Column(
                children: [
                  ListTile(
                    title: Text(
                      L10n.of(context).storage_total_size,
                      style: TextStyle(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: false,
                        ),
                      ),
                    ),
                    trailing:
                        fileSizeTriling(storageInfoAsync.value?.totalSizeStr),
                  ),
                  ListTile(
                    title: Text(
                      L10n.of(context).storage_database_file,
                      style: TextStyle(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: false,
                        ),
                      ),
                    ),
                    trailing: fileSizeTriling(
                      storageInfoAsync.value?.databaseSizeStr,
                    ),
                  ),
                  ListTile(
                    title: Text(
                      L10n.of(context).storage_log_file,
                      style: TextStyle(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: false,
                        ),
                      ),
                    ),
                    trailing:
                        fileSizeTriling(storageInfoAsync.value?.logSizeStr),
                  ),
                  ListTile(
                    title: Text(
                      L10n.of(context).storage_cache_file,
                      style: TextStyle(
                        color: DesignSystem.getSettingsTextColor(
                          context,
                          isPrimary: false,
                        ),
                      ),
                    ),
                    trailing:
                        cacheSizeTriling(storageInfoAsync.value?.cacheSizeStr),
                  ),
                  Column(
                    children: [
                      ListTile(
                        title: Text(
                          L10n.of(context).storage_data_file,
                          style: TextStyle(
                            color: DesignSystem.getSettingsTextColor(
                              context,
                              isPrimary: false,
                            ),
                          ),
                        ),
                        trailing: fileSizeTriling(
                          storageInfoAsync.value?.dataFilesSizeStr,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          left: DesignSystem.spaceL -
                              DesignSystem.spaceXS, // 20.0
                        ),
                        child: Column(
                          children: [
                            ListTile(
                              title: Text(
                                L10n.of(context).storage_book_file,
                                style: TextStyle(
                                  color: DesignSystem.getSettingsTextColor(
                                    context,
                                    isPrimary: false,
                                  ),
                                ),
                              ),
                              trailing: fileSizeTriling(
                                storageInfoAsync.value?.booksSizeStr,
                              ),
                            ),
                            ListTile(
                              title: Text(
                                L10n.of(context).storage_cover_file,
                                style: TextStyle(
                                  color: DesignSystem.getSettingsTextColor(
                                    context,
                                    isPrimary: false,
                                  ),
                                ),
                              ),
                              trailing: fileSizeTriling(
                                storageInfoAsync.value?.coverSizeStr,
                              ),
                            ),
                            ListTile(
                              title: Text(
                                L10n.of(context).storage_font_file,
                                style: TextStyle(
                                  color: DesignSystem.getSettingsTextColor(
                                    context,
                                    isPrimary: false,
                                  ),
                                ),
                              ),
                              trailing: fileSizeTriling(
                                storageInfoAsync.value?.fontSizeStr,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),

        // Tab view for data files details
        SettingsSection(
          title: Text(L10n.of(context).storage_data_file_details),
          tiles: [
            CustomSettingsTile(
              child: Padding(
                padding: const EdgeInsets.all(DesignSystem.spaceM), // 16.0
                child: Column(
                  children: [
                    TabBar(
                      controller: _tabController,
                      tabs: [
                        Tab(
                          text: L10n.of(context).storage_book_file,
                          icon: Icon(AdaptiveIcons.book),
                        ),
                        Tab(
                          text: L10n.of(context).storage_cover_file,
                          icon: Icon(AdaptiveIcons.image),
                        ),
                        Tab(
                          text: L10n.of(context).storage_font_file,
                          icon: Icon(AdaptiveIcons.fontDownloadOutlinedReading),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: ResponsiveSystem.getScreenHeight(context) -
                          ResponsiveSystem.getScreenPadding(context).top -
                          ResponsiveSystem.getScreenPadding(context).bottom -
                          kToolbarHeight -
                          140,
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          // Books tab
                          storageInfoAsync.when(
                            data: (_) => DataFilesDetailTab(
                              title: L10n.of(context).storage_book_file,
                              icon: AdaptiveIcons.book,
                              listFiles: ref
                                  .read(storageInfoProvider.notifier)
                                  .listBookFiles(),
                              showDelete: false,
                              ref: ref,
                            ),
                            loading: () => const Center(
                              child: CircularProgressIndicator.adaptive(),
                            ),
                            error: (_, __) => Center(
                              child: Text(L10n.of(context).common_error),
                            ),
                          ),
                          // Covers tab
                          storageInfoAsync.when(
                            data: (_) => DataFilesDetailTab(
                              title: L10n.of(context).storage_cover_file,
                              icon: AdaptiveIcons.image,
                              listFiles: ref
                                  .read(storageInfoProvider.notifier)
                                  .listCoverFiles(),
                              showDelete: false,
                              ref: ref,
                            ),
                            loading: () => const Center(
                              child: CircularProgressIndicator.adaptive(),
                            ),
                            error: (_, __) => Center(
                              child: Text(L10n.of(context).common_error),
                            ),
                          ),
                          // Fonts tab
                          storageInfoAsync.when(
                            data: (_) => DataFilesDetailTab(
                              title: L10n.of(context).storage_font_file,
                              icon: AdaptiveIcons.fontDownloadOutlinedReading,
                              listFiles: ref
                                  .read(storageInfoProvider.notifier)
                                  .listFontFiles(),
                              showDelete: true,
                              ref: ref,
                            ),
                            loading: () => const Center(
                              child: CircularProgressIndicator.adaptive(),
                            ),
                            error: (_, __) => Center(
                              child: Text(L10n.of(context).common_error),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

// Dictionary Preloading Tile
class DictionaryPreloadingTile extends StatefulWidget {
  const DictionaryPreloadingTile({super.key});

  @override
  State<DictionaryPreloadingTile> createState() =>
      _DictionaryPreloadingTileState();
}

class _DictionaryPreloadingTileState extends State<DictionaryPreloadingTile> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final preloadEnabled = Prefs().preloadDictionary;
    final preloadStatus = Prefs().dictionaryPreloadStatus;
    final entryCount = Prefs().dictionaryEntryCount;

    String getStatusText() {
      switch (preloadStatus) {
        case 0:
          return 'Not loaded';
        case 1:
          return 'Loading...';
        case 2:
          return 'Loaded ($entryCount entries)';
        default:
          return 'Unknown';
      }
    }

    return Column(
      children: [
        SwitchListTile(
          title: const Text('Preload Full Dictionary (Recommended)'),
          subtitle: const Text(
            'Load the entire dictionary into memory for instant lookups. Provides significantly faster performance with minimal memory usage (~40MB).',
          ),
          value: preloadEnabled,
          onChanged: _isLoading
              ? null
              : (value) async {
                  setState(() {
                    _isLoading = true;
                  });

                  Prefs().preloadDictionary = value;

                  if (value) {
                    // Start preloading
                    final success =
                        await DictionaryService().preloadDictionary();
                    if (!success && mounted) {
                      AnxToast.show('Failed to preload dictionary');
                    }
                  } else {
                    // Unload dictionary
                    DictionaryService().unloadDictionary();
                  }

                  if (mounted) {
                    setState(() {
                      _isLoading = false;
                    });
                  }
                },
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceM, // 16.0
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Status: ${getStatusText()}'),
              if (_isLoading)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2.0),
                ),
            ],
          ),
        ),
        const SizedBox(height: DesignSystem.spaceS), // 8.0
        if (preloadStatus == 2)
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: DesignSystem.spaceM, // 16.0
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading
                        ? null
                        : () async {
                            setState(() {
                              _isLoading = true;
                            });

                            DictionaryService().unloadDictionary();

                            if (mounted) {
                              setState(() {
                                _isLoading = false;
                              });
                            }
                          },
                    child: const Text('Unload Dictionary'),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

// Tab content for data files details
class DataFilesDetailTab extends StatelessWidget {
  final String title;
  final IconData icon;
  final Future<List<File>> listFiles;
  final bool showDelete;
  final WidgetRef ref;

  const DataFilesDetailTab({
    super.key,
    required this.title,
    required this.icon,
    required this.listFiles,
    this.showDelete = false,
    required this.ref,
  });

  @override
  Widget build(BuildContext context) {
    String formatSize(int bytes) {
      if (bytes <= 0) return '0 B';

      const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
      var i = (log(bytes) / log(1024)).floor();
      return '${(bytes / pow(1024, i)).toStringAsFixed(2)} ${suffixes[i]}';
    }

    Widget fileSizeWidget(File file) {
      return FutureBuilder<int>(
        future: file.length(),
        builder: (context, snapshot) {
          return Text(
            formatSize(snapshot.data ?? 0),
            style: TextStyle(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
            ),
          );
        },
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: FutureBuilder<List<File>>(
            future: listFiles,
            builder: (context, snapshot) {
              if (snapshot.hasData) {
                final files = snapshot.data!;
                files.sort((a, b) => b.lengthSync().compareTo(a.lengthSync()));
                return ListView.builder(
                  itemCount: files.length,
                  itemBuilder: (context, index) {
                    final file = files[index];
                    return ListTile(
                      title: Text(
                        file.path.split(Platform.pathSeparator).last,
                        style: TextStyle(
                          color: DesignSystem.getSettingsTextColor(
                            context,
                            isPrimary: false,
                          ),
                        ),
                      ),
                      subtitle: showDelete ? fileSizeWidget(file) : null,
                      trailing: showDelete
                          ? file.path.endsWith('SourceHanSerifSC-Regular.otf')
                              ? null
                              : DeleteConfirm(
                                  delete: () {
                                    snapshot.data!.remove(file);
                                    ref
                                        .read(storageInfoProvider.notifier)
                                        .deleteFile(file);
                                  },
                                  deleteIcon: Icon(AdaptiveIcons.delete),
                                  confirmIcon: Icon(AdaptiveIcons.success),
                                )
                          : fileSizeWidget(file),
                    );
                  },
                );
              }
              return const Center(child: CircularProgressIndicator.adaptive());
            },
          ),
        ),
      ],
    );
  }
}
