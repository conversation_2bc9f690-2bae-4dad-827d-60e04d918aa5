import 'dart:async';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/dao/reading_time.dart';
import 'package:dasso_reader/dao/theme.dart';
import 'package:dasso_reader/enums/sync_direction.dart';

import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/models/read_theme.dart';
import 'package:dasso_reader/page/book_detail.dart';
import 'package:dasso_reader/page/book_player/epub_player.dart';
import 'package:dasso_reader/providers/ai_chat.dart';
import 'package:dasso_reader/providers/anx_webdav.dart';
import 'package:dasso_reader/service/ai/ai_dio.dart';
import 'package:dasso_reader/service/ai/prompt_generate.dart';

import 'package:dasso_reader/utils/toast/common.dart';
import 'package:dasso_reader/utils/ui/status_bar.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:dasso_reader/widgets/ai_chat_stream.dart';
import 'package:dasso_reader/widgets/ai_stream.dart';
import 'package:dasso_reader/widgets/reading_page/notes_widget.dart';
import 'package:dasso_reader/models/reading_time.dart';
import 'package:dasso_reader/widgets/reading_page/progress_widget.dart';
import 'package:dasso_reader/widgets/reading_page/search_page.dart';

import 'package:dasso_reader/widgets/reading_page/tts_widget.dart';
import 'package:dasso_reader/widgets/reading_page/style_widget.dart';
import 'package:dasso_reader/widgets/reading_page/toc_widget.dart';
import 'package:dasso_reader/widgets/reading_page/light_widget.dart';
import 'package:dasso_reader/widgets/reading_page/reading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class ReadingPage extends ConsumerStatefulWidget {
  const ReadingPage({super.key, required this.book, this.cfi});

  final Book book;
  final String? cfi;

  @override
  ConsumerState<ReadingPage> createState() => ReadingPageState();
}

final GlobalKey<ReadingPageState> readingPageKey =
    GlobalKey<ReadingPageState>();
final epubPlayerKey = GlobalKey<EpubPlayerState>();

class ReadingPageState extends ConsumerState<ReadingPage>
    with WidgetsBindingObserver {
  late Book _book;
  Widget _currentPage = const SizedBox(
    height: 1.0, // Minimal height placeholder
  );
  final Stopwatch _readTimeWatch = Stopwatch();
  Timer? _awakeTimer;
  bool bottomBarOffstage = true;
  bool tocOffstage = true;
  Widget? _tocWidget;
  String heroTag = 'preventHeroWhenStart';
  Widget? _aiChat;
  final aiChatKey = GlobalKey<AiChatStreamState>();

  // Bookmark state management
  bool bookmarkExists = false;

  late FocusOnKeyEventCallback _handleKeyEvent;

  @override
  void initState() {
    if (widget.book.isDeleted) {
      Navigator.pop(context);
      AnxToast.show(L10n.of(context).book_deleted);
      return;
    }
    if (Prefs().hideStatusBar) {
      hideStatusBar();
    }
    WidgetsBinding.instance.addObserver(this);
    _readTimeWatch.start();
    setAwakeTimer(Prefs().awakeTime);

    _book = widget.book;
    _addKeyboardListener();

    // delay 1000ms to prevent hero animation
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        setState(() {
          heroTag = _book.coverFullPath;
        });
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    AnxWebdav().syncData(SyncDirection.upload, ref);
    _readTimeWatch.stop();
    _awakeTimer?.cancel();
    WakelockPlus.disable();
    showStatusBar();
    WidgetsBinding.instance.removeObserver(this);
    insertReadingTime(
      ReadingTime(
        bookId: _book.id,
        readingTime: _readTimeWatch.elapsed.inSeconds,
      ),
    );
    audioHandler.stop();
    _removeKeyboardListener();
    super.dispose();
  }

  void _addKeyboardListener() {
    _handleKeyEvent = (FocusNode node, KeyEvent event) {
      if (!Prefs().volumeKeyTurnPage) {
        return KeyEventResult.ignored;
      }

      if (event is KeyDownEvent) {
        if (event.physicalKey == PhysicalKeyboardKey.audioVolumeUp) {
          epubPlayerKey.currentState?.prevPage();
          return KeyEventResult.handled;
        } else if (event.physicalKey == PhysicalKeyboardKey.audioVolumeDown) {
          epubPlayerKey.currentState?.nextPage();
          return KeyEventResult.handled;
        }
      }
      return KeyEventResult.ignored;
    };
  }

  void _removeKeyboardListener() {
    _handleKeyEvent = (FocusNode node, KeyEvent event) {
      return KeyEventResult.ignored;
    };
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused) {
      epubPlayerKey.currentState!.saveReadingProgress();
      _readTimeWatch.stop();
    } else if (state == AppLifecycleState.resumed) {
      _readTimeWatch.start();
    }
  }

  Future<void> setAwakeTimer(int minutes) async {
    _awakeTimer?.cancel();
    _awakeTimer = null;
    WakelockPlus.enable();
    _awakeTimer = Timer.periodic(Duration(minutes: minutes), (timer) {
      WakelockPlus.disable();
      _awakeTimer?.cancel();
      _awakeTimer = null;
    });
  }

  void resetAwakeTimer() {
    setAwakeTimer(Prefs().awakeTime);
  }

  void updateState() {
    if (mounted) {
      setState(() {
        bookmarkExists = epubPlayerKey.currentState?.bookmarkExists ?? false;
      });
    }
  }

  void showBottomBar() {
    setState(() {
      showStatusBarWithoutResize();

      // Apply StatusBar styling based on reading background color
      final backgroundColor =
          Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));
      applyStatusBarForBackground(backgroundColor);

      bottomBarOffstage = false;
      _removeKeyboardListener();
    });
  }

  void hideBottomBar() {
    setState(() {
      tocOffstage = true;
      _currentPage = const SizedBox(
        height: 1.0, // Minimal height placeholder
      );
      bottomBarOffstage = true;
      if (Prefs().hideStatusBar) {
        hideStatusBar();
      }
      _addKeyboardListener();
    });
  }

  void showOrHideAppBarAndBottomBar(bool show) {
    if (show) {
      showBottomBar();
    } else {
      hideBottomBar();
    }
  }

  Future<void> tocHandler() async {
    // Use app theme colors instead of reading theme colors for better dark mode support
    final backgroundColor = Theme.of(context).colorScheme.surface;
    final textColor = Theme.of(context).colorScheme.onSurface;

    setState(() {
      _tocWidget = TocWidget(
        tocItems: epubPlayerKey.currentState!.toc,
        epubPlayerKey: epubPlayerKey,
        hideAppBarAndBottomBar: showOrHideAppBarAndBottomBar,
        book: _book,
        backgroundColor: backgroundColor,
        textColor: textColor,
      );
      _currentPage = const SizedBox(
        height: 1.0, // Minimal height placeholder
      );
      tocOffstage = false;
    });
  }

  // Cache colors to avoid repeated parsing
  Color? _cachedBackgroundColor;
  Color? _cachedTextColor;
  String? _lastThemeKey;

  void _updateCachedColors() {
    // Use app theme colors instead of reading theme colors for better dark mode support
    final appTheme = Theme.of(context);
    final themeKey =
        '${appTheme.brightness}_${appTheme.colorScheme.surface}_${appTheme.colorScheme.onSurface}';

    if (_lastThemeKey != themeKey) {
      _cachedBackgroundColor = appTheme.colorScheme.surface;
      _cachedTextColor = appTheme.colorScheme.onSurface;
      _lastThemeKey = themeKey;
    }
  }

  void noteHandler() {
    _updateCachedColors();

    setState(() {
      _currentPage = ReadingNotes(
        book: _book,
        backgroundColor: _cachedBackgroundColor!,
        textColor: _cachedTextColor!,
      );
    });
  }

  void progressHandler() {
    _updateCachedColors();

    setState(() {
      _currentPage = ProgressWidget(
        epubPlayerKey: epubPlayerKey,
        showOrHideAppBarAndBottomBar: showOrHideAppBarAndBottomBar,
        backgroundColor: _cachedBackgroundColor!,
        textColor: _cachedTextColor!,
      );
    });
  }

  Future<void> styleHandler(StateSetter modalSetState) async {
    _updateCachedColors();

    List<ReadTheme> themes = await selectThemes();
    setState(() {
      _currentPage = StyleWidget(
        themes: themes,
        epubPlayerKey: epubPlayerKey,
        setCurrentPage: (Widget page) {
          modalSetState(() {
            _currentPage = page;
          });
        },
        backgroundColor: _cachedBackgroundColor!,
        textColor: _cachedTextColor!,
      );
    });
  }

  Future<void> lightHandler(StateSetter modalSetState) async {
    List<ReadTheme> themes = await selectThemes();
    setState(() {
      _currentPage = LightWidget(
        themes: themes,
        epubPlayerKey: epubPlayerKey,
        setCurrentPage: (Widget page) {
          modalSetState(() {
            _currentPage = page;
          });
        },
      );
    });
  }

  void ttsHandler() async {
    // Use app theme colors instead of reading theme colors for better dark mode support
    final backgroundColor = Theme.of(context).colorScheme.surface;
    final textColor = Theme.of(context).colorScheme.onSurface;

    setState(() {
      _currentPage = TtsWidget(
        epubPlayerKey: epubPlayerKey,
        backgroundColor: backgroundColor,
        textColor: textColor,
      );
    });
  }

  void readingHandler() async {
    // Use app theme colors instead of reading theme colors for better dark mode support
    final backgroundColor = Theme.of(context).colorScheme.surface;
    final textColor = Theme.of(context).colorScheme.onSurface;

    setState(() {
      _currentPage = ReadingWidget(
        epubPlayerKey: epubPlayerKey,
        backgroundColor: backgroundColor,
        textColor: textColor,
      );
    });
  }

  Future<void> onLoadEnd() async {
    // Check bookmark status for current page
    epubPlayerKey.currentState?.checkCurrentPageBookmark();

    if (Prefs().autoSummaryPreviousContent) {
      final previousContent =
          await epubPlayerKey.currentState!.previousContent(2000);

      // Get reading theme colors for the dialog
      final readingTextColor =
          Color(int.parse('0x${Prefs().readTheme.textColor}'));
      final readingBackgroundColor =
          Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));

      SmartDialog.show<void>(
        builder: (context) => AlertDialog(
          backgroundColor: readingBackgroundColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusM), // 16.0
          ),
          titleTextStyle: TextStyle(
            color: readingTextColor,
            fontSize:
                DesignSystem.fontSizeL + DesignSystem.spaceXS, // 18.0 (14 + 4)
            fontWeight: FontWeight.bold,
          ),
          contentTextStyle: TextStyle(color: readingTextColor),
          title: Text(L10n.of(context).reading_page_summary_previous_content),
          content: AiStream(
            prompt: generatePromptSummaryThePreviousContent(previousContent),
          ),
        ),
        onDismiss: () {
          AiDio.instance.cancel();
        },
      );
    }
  }

  Future<void> showAiChat({
    String? content,
    bool sendImmediate = false,
  }) async {
    // Get reading theme colors for the AI chat
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));
    final readingBackgroundColor =
        Color(int.parse('0x${Prefs().readTheme.backgroundColor}'));

    // Use orientation-aware layout for AI chat
    final currentContext = navigatorKey.currentContext!;
    final isLandscape = ResponsiveSystem.isLandscape(currentContext);
    final shouldUseBottomSheet = !DesignSystem.isTablet(currentContext) ||
        (DesignSystem.isMobile(currentContext) && !isLandscape);

    if (shouldUseBottomSheet) {
      // Get orientation-aware constraints
      final constraints =
          ResponsiveSystem.getOrientationAwareBottomSheetConstraints(
        currentContext,
      );

      showModalBottomSheet<void>(
        context: currentContext,
        isScrollControlled: true,
        showDragHandle: true,
        backgroundColor: readingBackgroundColor,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        builder: (context) => Theme(
          data: ThemeData(
            scaffoldBackgroundColor: readingBackgroundColor,
            colorScheme: ColorScheme.fromSeed(
              seedColor: Theme.of(context).colorScheme.primary,
              brightness: ThemeData.estimateBrightnessForColor(
                readingBackgroundColor,
              ),
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
              secondary: Theme.of(context).colorScheme.secondary,
              surface: readingBackgroundColor,
              onSurface: readingTextColor,
            ),
            textTheme: Theme.of(context).textTheme.apply(
                  bodyColor: readingTextColor,
                  displayColor: readingTextColor,
                ),
          ),
          child: PointerInterceptor(
            child: ConstrainedBox(
              constraints: constraints,
              child: Padding(
                padding: const EdgeInsets.only(
                  top: DesignSystem.spaceS, // 8.0
                ),
                child: AiChatStream(
                  key: aiChatKey,
                  initialMessage: content,
                  sendImmediate: sendImmediate,
                ),
              ),
            ),
          ),
        ),
      );
    } else {
      setState(() {
        _aiChat = SizedBox(
          width: 300,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              IconButton(
                onPressed: () {
                  setState(() {
                    _aiChat = null;
                  });
                },
                icon: Icon(AdaptiveIcons.close),
              ),
              Expanded(
                child: AiChatStream(
                  key: aiChatKey,
                  initialMessage: content,
                  sendImmediate: sendImmediate,
                ),
              ),
            ],
          ),
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use app theme colors instead of reading theme colors for better dark mode support
    final backgroundColor = Theme.of(context).colorScheme.surface;
    final textColor = Theme.of(context).colorScheme.onSurface;

    // Watch the active search term provider
    final activeSearchTerm = ref.watch(activeSearchTermProvider);
    final bool isSearchActive =
        activeSearchTerm != null && activeSearchTerm.isNotEmpty;

    // Add a key for the more options button
    final moreButtonKey = GlobalKey();

    // Note: Removed custom readingTheme to match anx-reader's simple AppBar/BottomBar styling

    var aiButton = IconButton(
      icon: Icon(AdaptiveIcons.autoAwesome),
      onPressed: () async {
        if (DesignSystem.isTablet(context) && _aiChat != null) {
          setState(() {
            _aiChat = null;
          });
          return;
        }

        showOrHideAppBarAndBottomBar(false);
        final String chapterContent =
            await epubPlayerKey.currentState!.theChapterContent();
        final sendImmediate = (ref.read(aiChatProvider).value?.isEmpty ?? true);
        final content = generatePromptSummaryTheChapter(chapterContent);
        showAiChat(
          content: sendImmediate ? content : null,
          sendImmediate: sendImmediate,
        );
      },
    );

    Offstage controller = Offstage(
      offstage: bottomBarOffstage,
      child: PointerInterceptor(
        child: Stack(
          children: [
            Positioned.fill(
              child: GestureDetector(
                onTap: () {
                  showOrHideAppBarAndBottomBar(false);
                },
                child: Container(
                  color: Colors.black.withAlpha(15),
                ),
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    // Bottom edge shadow only - creates floating appearance while maintaining top edge integration
                    boxShadow: [
                      BoxShadow(
                        color: DesignSystem.getStateLayerColor(
                          Theme.of(context).colorScheme.shadow,
                          0.15,
                        ),
                        blurRadius: DesignSystem.getAdjustedElevation(
                          DesignSystem.elevationM,
                        ),
                        offset: Offset(
                          0,
                          DesignSystem.getAdjustedElevation(
                                DesignSystem.elevationM,
                              ) /
                              2,
                        ),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: AppBar(
                    elevation:
                        0, // Remove default elevation since we're using custom shadow
                    backgroundColor: Colors.transparent,
                    surfaceTintColor: Colors.transparent,
                    title: Text(_book.title, overflow: TextOverflow.ellipsis),
                    leading: SemanticHelpers.button(
                      context: context,
                      label: 'Back to bookshelf',
                      hint: 'Close reading page and return to bookshelf',
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: IconButton(
                        icon: Icon(AdaptiveIcons.arrowBack),
                        onPressed: () {
                          // close reading page
                          Navigator.pop(context);
                        },
                      ),
                    ),
                    actions: [
                      aiButton,
                      IconButton(
                        onPressed: () {
                          if (bookmarkExists) {
                            // Remove bookmark using JavaScript function
                            epubPlayerKey.currentState!.webViewController
                                .evaluateJavascript(
                              source:
                                  'window.removeBookmarkAtCurrentLocation()',
                            );
                          } else {
                            epubPlayerKey.currentState!.addBookmarkHere();
                          }
                        },
                        icon: bookmarkExists
                            ? Icon(AdaptiveIcons.bookmarkFilled)
                            : Icon(AdaptiveIcons.bookmark),
                      ),
                      IconButton(
                        key: moreButtonKey,
                        icon: Icon(AdaptiveIcons.moreVertical),
                        onPressed: () {
                          // Show popup menu using the button's position
                          final RenderBox button = moreButtonKey.currentContext!
                              .findRenderObject() as RenderBox;
                          final Offset buttonPosition =
                              button.localToGlobal(Offset.zero);

                          // Calculate position to show menu below the button
                          showMenu(
                            context: context,
                            position: RelativeRect.fromLTRB(
                              buttonPosition.dx, // left
                              buttonPosition.dy +
                                  button.size.height, // top (below button)
                              buttonPosition.dx + button.size.width, // right
                              0, // bottom
                            ),
                            // Apply reading theme colors to the popup menu
                            color: Color(
                              int.parse(
                                '0x${Prefs().readTheme.backgroundColor}',
                              ),
                            ),
                            elevation:
                                0, // Remove shadow for a more integrated look
                            // Style menu items to use reading theme text color
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                DesignSystem.radiusS, // 8.0
                              ),
                            ),
                            items: [
                              PopupMenuItem<void>(
                                child: Row(
                                  children: [
                                    Icon(
                                      AdaptiveIcons.search,
                                      color: Color(
                                        int.parse(
                                          '0x${Prefs().readTheme.textColor}',
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: DesignSystem.spaceS, // 8.0
                                    ),
                                    Text(
                                      'Search',
                                      style: TextStyle(
                                        color: Color(
                                          int.parse(
                                            '0x${Prefs().readTheme.textColor}',
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                onTap: () => _navigateToSearchPageDelayed(),
                              ),
                              PopupMenuItem<void>(
                                child: Row(
                                  children: [
                                    Icon(
                                      AdaptiveIcons.infoOutline,
                                      color: Color(
                                        int.parse(
                                          '0x${Prefs().readTheme.textColor}',
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: DesignSystem.spaceS, // 8.0
                                    ),
                                    Text(
                                      'Book Details',
                                      style: TextStyle(
                                        color: Color(
                                          int.parse(
                                            '0x${Prefs().readTheme.textColor}',
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                onTap: () => _navigateToBookDetailDelayed(),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                BottomSheet(
                  onClosing: () {},
                  enableDrag: false,
                  backgroundColor:
                      backgroundColor, // Match panels background color
                  elevation:
                      0, // Remove elevation since only panels should have shadows
                  shape: const RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.zero, // Remove rounded corners and borders
                  ),
                  builder: (context) => SafeArea(
                    top: false,
                    child: Container(
                      constraints: const BoxConstraints(maxWidth: 600),
                      child: StatefulBuilder(
                        builder: (BuildContext context, StateSetter setState) {
                          return IntrinsicHeight(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Expanded(child: _currentPage),
                                Offstage(
                                  offstage:
                                      tocOffstage || _currentPage is! SizedBox,
                                  child: _tocWidget,
                                ),
                                // Only show fixed progress when no other panel is displayed
                                Offstage(
                                  offstage: !(_currentPage is SizedBox &&
                                      tocOffstage),
                                  child: FixedProgressWidget(
                                    epubPlayerKey: epubPlayerKey,
                                    backgroundColor: backgroundColor,
                                    textColor: textColor,
                                  ),
                                ),
                                // Add consistent padding between progress slider and bottom bar
                                const SizedBox(
                                  height: DesignSystem.spaceL,
                                ), // 24.0 (preserves exact spacing)
                                Container(
                                  decoration: const BoxDecoration(
                                      // Remove the border to unify with progress widget
                                      ),
                                  padding: EdgeInsets.zero,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceAround,
                                    children: [
                                      SemanticHelpers.button(
                                        context: context,
                                        label: 'Table of Contents',
                                        hint:
                                            'Navigate to different chapters and sections',
                                        onTap: tocHandler,
                                        child: IconButton(
                                          icon: Icon(AdaptiveIcons.toc),
                                          onPressed: tocHandler,
                                        ),
                                      ),
                                      SemanticHelpers.button(
                                        context: context,
                                        label: 'Text Style Settings',
                                        hint:
                                            'Adjust font size, family, and text appearance',
                                        onTap: () {
                                          styleHandler(setState);
                                        },
                                        child: IconButton(
                                          icon: Text(
                                            'A',
                                            style: TextStyle(
                                              fontSize: DesignSystem
                                                  .getAdjustedFontSize(
                                                context,
                                                DesignSystem.fontSizeHeadingS,
                                              ),
                                              fontWeight: DesignSystem
                                                  .getAdjustedFontWeight(
                                                FontWeight.w500,
                                                text: 'A',
                                              ),
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSurface,
                                            ),
                                          ),
                                          onPressed: () {
                                            styleHandler(setState);
                                          },
                                        ),
                                      ),
                                      // Using Wb Sunny icon that exactly matches the design
                                      SemanticHelpers.button(
                                        context: context,
                                        label: 'Theme Settings',
                                        hint:
                                            'Change background color, brightness, and reading theme',
                                        onTap: () {
                                          lightHandler(setState);
                                        },
                                        child: IconButton(
                                          icon: Icon(
                                            AdaptiveIcons.wbSunnyOutlined,
                                            size: DesignSystem.widgetIconSizeMedium,
                                          ),
                                          onPressed: () {
                                            lightHandler(setState);
                                          },
                                        ),
                                      ),
                                      // Using Auto Stories icon to exactly match the design
                                      SemanticHelpers.button(
                                        context: context,
                                        label: 'Reading Settings',
                                        hint:
                                            'Adjust reading preferences and display options',
                                        onTap: readingHandler,
                                        child: IconButton(
                                          icon: Icon(
                                            AdaptiveIcons.autoStoriesOutlined,
                                            size: 25,
                                          ),
                                          onPressed: readingHandler,
                                        ),
                                      ),
                                      SemanticHelpers.button(
                                        context: context,
                                        label: 'Text-to-Speech',
                                        hint:
                                            'Listen to the book with voice narration',
                                        onTap: ttsHandler,
                                        child: IconButton(
                                          icon: Icon(AdaptiveIcons.headphones),
                                          onPressed: ttsHandler,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Hero(
        tag: Prefs().openBookAnimation ? _book.coverFullPath : heroTag,
        child: FittedBox(
          fit: BoxFit.scaleDown,
          child: SizedBox(
            height: ResponsiveSystem.getScreenHeight(context),
            width: ResponsiveSystem.getScreenWidth(context),
            child: Scaffold(
              resizeToAvoidBottomInset: false,
              body: Stack(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: MouseRegion(
                          onHover: (PointerHoverEvent detail) {
                            var y = detail.position.dy;
                            if (y < 30 ||
                                y >
                                    ResponsiveSystem.getScreenHeight(context) -
                                        30) {
                              showOrHideAppBarAndBottomBar(true);
                            }
                          },
                          child: Focus(
                            focusNode: FocusNode(),
                            onKeyEvent: _handleKeyEvent,
                            child: EpubPlayer(
                              key: epubPlayerKey,
                              book: _book,
                              cfi: widget.cfi,
                              showOrHideAppBarAndBottomBar:
                                  showOrHideAppBarAndBottomBar,
                              onLoadEnd: onLoadEnd,
                              updateParent: updateState,
                            ),
                          ),
                        ),
                      ),
                      _aiChat != null
                          ? const VerticalDivider(width: 1)
                          : const SizedBox.shrink(),
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 200),
                        child: _aiChat,
                      ),
                    ],
                  ),
                  controller,
                  if (isSearchActive && bottomBarOffstage)
                    Positioned(
                      bottom: DesignSystem.spaceXL -
                          2, // 30.0 (preserves exact position)
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Material(
                          elevation: DesignSystem
                              .elevationM, // 4.0 (preserves exact elevation)
                          borderRadius: BorderRadius.circular(
                            DesignSystem.radiusCircle + 2,
                          ), // 30.0 (preserves exact radius)
                          color: backgroundColor.withValues(alpha: 0.9),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: DesignSystem.spaceS,
                            ), // 8.0 (preserves exact spacing)
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SemanticHelpers.button(
                                  context: context,
                                  label: 'Search in Book',
                                  hint:
                                      'Open search page to find text in the current book',
                                  onTap: () {
                                    // Navigate to SearchPage
                                    Navigator.of(context).push(
                                      PageRouteBuilder<void>(
                                        pageBuilder: (
                                          context,
                                          animation,
                                          secondaryAnimation,
                                        ) =>
                                            SearchPage(
                                          // Need to pass all required args
                                          hideAppBarAndBottomBar:
                                              showOrHideAppBarAndBottomBar,
                                          epubPlayerKey: epubPlayerKey,
                                          book: _book, // Use _book from state
                                          backgroundColor: backgroundColor,
                                          textColor: textColor,
                                        ),
                                        // Optional: Add transitions if desired
                                        transitionDuration:
                                            const Duration(milliseconds: 200),
                                        transitionsBuilder: (
                                          context,
                                          animation,
                                          secondaryAnimation,
                                          child,
                                        ) =>
                                            FadeTransition(
                                          opacity: animation,
                                          child: child,
                                        ),
                                      ),
                                    );
                                  },
                                  child: IconButton(
                                    icon: Icon(AdaptiveIcons.search),
                                    tooltip: L10n.of(context)
                                        .reading_page_search_tooltip, // Add tooltip
                                    onPressed: () {
                                      // Navigate to SearchPage
                                      Navigator.of(context).push(
                                        PageRouteBuilder<void>(
                                          pageBuilder: (
                                            context,
                                            animation,
                                            secondaryAnimation,
                                          ) =>
                                              SearchPage(
                                            // Need to pass all required args
                                            hideAppBarAndBottomBar:
                                                showOrHideAppBarAndBottomBar,
                                            epubPlayerKey: epubPlayerKey,
                                            book: _book, // Use _book from state
                                            backgroundColor: backgroundColor,
                                            textColor: textColor,
                                          ),
                                          // Optional: Add transitions if desired
                                          transitionDuration:
                                              const Duration(milliseconds: 200),
                                          transitionsBuilder: (
                                            context,
                                            animation,
                                            secondaryAnimation,
                                            child,
                                          ) =>
                                              FadeTransition(
                                            opacity: animation,
                                            child: child,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                                SemanticHelpers.button(
                                  context: context,
                                  label: 'Clear Search',
                                  hint:
                                      'Remove search highlights and close search mode',
                                  onTap: () {
                                    // Clear search highlights and provider state
                                    epubPlayerKey.currentState?.clearSearch();
                                    ref
                                        .read(activeSearchTermProvider.notifier)
                                        .state = null;
                                    // No need to setState here as the provider watch will rebuild
                                  },
                                  child: IconButton(
                                    icon: Icon(AdaptiveIcons.close),
                                    tooltip: L10n.of(context)
                                        .reading_page_clear_search_tooltip, // Add tooltip
                                    onPressed: () {
                                      // Clear search highlights and provider state
                                      epubPlayerKey.currentState?.clearSearch();
                                      ref
                                          .read(
                                            activeSearchTermProvider.notifier,
                                          )
                                          .state = null;
                                      // No need to setState here as the provider watch will rebuild
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToSearchPageDelayed() async {
    // Delay to allow menu to close
    await Future<void>.delayed(const Duration(milliseconds: 100));
    if (!mounted) return;

    final backgroundColor = Color(
      int.parse('0x${Prefs().readTheme.backgroundColor}'),
    );
    final textColor = Color(
      int.parse('0x${Prefs().readTheme.textColor}'),
    );

    Navigator.of(context).push(
      PageRouteBuilder<void>(
        pageBuilder: (context, animation, secondaryAnimation) => SearchPage(
          hideAppBarAndBottomBar: showOrHideAppBarAndBottomBar,
          epubPlayerKey: epubPlayerKey,
          book: _book,
          backgroundColor: backgroundColor,
          textColor: textColor,
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToBookDetailDelayed() async {
    // Delay to allow menu to close
    await Future<void>.delayed(const Duration(milliseconds: 100));
    if (!mounted) return;

    Navigator.push(
      context,
      PageRouteBuilder<void>(
        pageBuilder: (context, animation, secondaryAnimation) =>
            BookDetail(book: widget.book),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(0.0, 1.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }
}
