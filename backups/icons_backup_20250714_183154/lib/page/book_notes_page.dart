import 'package:dasso_reader/dao/book_note.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/models/book_note.dart';
import 'package:dasso_reader/service/notes/export_notes.dart';
import 'package:dasso_reader/widgets/bookshelf/book_cover.dart';
import 'package:dasso_reader/widgets/book_notes/book_notes_list.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/page/book_detail.dart';
import 'package:dasso_reader/widgets/highlight_digit.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:flutter/material.dart';
import 'package:icons_plus/icons_plus.dart';
import 'package:dasso_reader/config/design_system.dart';

class BookNotesPage extends StatefulWidget {
  const BookNotesPage({
    super.key,
    required this.book,
    required this.numberOfNotes,
    required this.isMobile,
  });

  final Book book;
  final int numberOfNotes;
  final bool isMobile;

  @override
  State<BookNotesPage> createState() => _BookNotesPageState();
}

class _BookNotesPageState extends State<BookNotesPage> {
  Widget bookInfo(BuildContext context, Book book, int numberOfNotes) {
    TextStyle titleStyle = TextStyle(
      fontSize: DesignSystem.fontSizeXL, // 24.0
      fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
      overflow: TextOverflow.ellipsis,
      color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
    );
    return Card(
      child: Container(
        padding: const EdgeInsets.all(
          DesignSystem.spaceS + DesignSystem.spaceXS, // 10.0 (8 + 2)
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            if (DesignSystem.isTablet(context)) {
              return Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          book.title,
                          style: titleStyle,
                          maxLines: 1,
                        ),
                        notesStatistic(context, numberOfNotes, book),
                        const SizedBox(
                          height: 25,
                        ),
                        operators(context, book),
                      ],
                    ),
                  ),
                  const SizedBox(
                    width: DesignSystem.spaceXL -
                        DesignSystem.spaceXS, // 30.0 (32 - 2)
                  ),
                  Hero(
                    tag: book.coverFullPath,
                    child: bookCover(
                      context,
                      book,
                      height: 180,
                      width: 120,
                    ),
                  ),
                ],
              );
            } else {
              return Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              book.title,
                              style: titleStyle,
                              maxLines: 2,
                            ),
                            notesStatistic(context, numberOfNotes, book),
                            const SizedBox(
                              height: 25,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        width: DesignSystem.spaceXL -
                            DesignSystem.spaceXS, // 30.0 (32 - 2)
                      ),
                      Hero(
                        tag: book.coverFullPath,
                        child: bookCover(
                          context,
                          book,
                          height: 180,
                          width: 120,
                        ),
                      ),
                    ],
                  ),
                  operators(context, book),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Future<void> handleExportNotes(
    BuildContext context,
    Book book, {
    List<BookNote>? notes,
  }) async {
    notes ??= await selectBookNotesByBookId(book.id);

    if (!context.mounted) return;

    showModalBottomSheet<void>(
      context: context,
      builder: (context) {
        return SizedBox(
          height: 100,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              operateButton(context, const Icon(Icons.copy), 'Copy', () {
                Navigator.pop(context);
                exportNotes(book, notes!, ExportType.copy);
              }),
              operateButton(
                  context,
                  // SvgPicture.asset('assets/icon/Markdown.svg'),
                  const Icon(IonIcons.logo_markdown),
                  'Markdown', () {
                Navigator.pop(context);
                exportNotes(book, notes!, ExportType.md);
              }),
              operateButton(context, const Icon(Icons.text_snippet), 'Text',
                  () {
                Navigator.pop(context);
                exportNotes(book, notes!, ExportType.txt);
              }),
              operateButton(context, const Icon(Icons.table_chart), 'CSV', () {
                Navigator.pop(context);
                exportNotes(book, notes!, ExportType.csv);
              }),
            ],
          ),
        );
      },
    );
  }

  Row operators(BuildContext context, Book book) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        operateButton(context, const Icon(Icons.details),
            L10n.of(context).notes_page_detail, () {
          AdaptiveNavigation.push(
            context,
            BookDetail(book: book),
          );
        }),
        // operateButton(context, Icons.search, 'Search', () {}),
        operateButton(context, const Icon(Icons.ios_share),
            L10n.of(context).notes_page_export, () {
          handleExportNotes(context, book);
        }),
        // operateButton(context, Icons.ios_share, 'Export', () {}),
      ],
    );
  }

  Widget operateButton(
    BuildContext context,
    Widget icon,
    String text,
    void Function() onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(DesignSystem.spaceS), // 8.0
        child: Column(
          children: [
            SizedBox(height: 40, width: 40, child: icon),
            Text(
              text,
              style: TextStyle(
                color: DesignSystem.getSettingsTextColor(
                  context,
                  isPrimary: false,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget notesStatistic(BuildContext context, int numberOfNotes, Book book) {
    TextStyle digitStyle = TextStyle(
      fontSize:
          DesignSystem.fontSizeXXL - DesignSystem.spaceXS, // 28.0 (32 - 4)
      fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
      color: DesignSystem.getSettingsTextColor(context, isPrimary: true),
    );
    TextStyle textStyle = TextStyle(
      fontSize: DesignSystem.fontSizeL + DesignSystem.spaceXS, // 18.0 (14 + 4)
      color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
      fontFamily: 'SourceHanSerif',
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        highlightDigit(
          context,
          L10n.of(context).notes_notes(numberOfNotes),
          textStyle,
          digitStyle,
        ),
        Text(
          L10n.of(context).notes_read_percentage(
            '${(book.readingPercentage * 100).toStringAsFixed(2)}%',
          ),
          style: TextStyle(
            color: DesignSystem.getSettingsTextColor(context, isPrimary: false),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isMobile
          ? AppBar(
              title: Text(
                widget.book.title,
                style: TextStyle(
                  color: DesignSystem.getSettingsTextColor(
                    context,
                    isPrimary: true,
                  ),
                ),
              ),
            )
          : null,
      extendBodyBehindAppBar: true,
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(
            DesignSystem.spaceL - DesignSystem.spaceXS, // 20.0
          ),
          children: [
            bookInfo(context, widget.book, widget.numberOfNotes),
            const SizedBox(
              height:
                  170.0, // Large spacing for visual separation - consider using DesignSystem constants
            ),
            BookNotesList(
              book: widget.book,
              reading: false,
              exportNotes: handleExportNotes,
              hideBookmarks: true,
            ),
          ],
        ),
      ),
    );
  }
}
