import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:dasso_reader/utils/performance/performance_metrics.dart';
import 'package:dasso_reader/config/design_system.dart';

/// Performance Dashboard widget for viewing performance metrics in the app
class PerformanceDashboard extends StatefulWidget {
  const PerformanceDashboard({super.key});

  @override
  State<PerformanceDashboard> createState() => _PerformanceDashboardState();
}

class _PerformanceDashboardState extends State<PerformanceDashboard> {
  late PerformanceReport _report;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPerformanceData();
  }

  void _loadPerformanceData() {
    setState(() {
      _isLoading = true;
    });

    // Get performance report
    _report = PerformanceMetrics().getPerformanceReport();

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This widget should never be shown in release mode
    // The settings option is hidden in release mode
    assert(
      !kReleaseMode,
      'Performance Dashboard should not be accessible in release mode',
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text('Performance Dashboard'),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              size: DesignSystem.getAdjustedIconSize(24.0),
            ),
            onPressed: _loadPerformanceData,
            tooltip: 'Refresh Data',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () async => _loadPerformanceData(),
              child: SingleChildScrollView(
                padding: DesignSystem.getAdaptiveContentPadding(context),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildOverallScoreCard(context),
                    DesignSystem.verticalSpaceL,
                    _buildFrameRateCard(),
                    DesignSystem.verticalSpaceM,
                    _buildStartupTimeCard(),
                    DesignSystem.verticalSpaceM,
                    _buildMemoryUsageCard(),
                    DesignSystem.verticalSpaceM,
                    _buildCPUUsageCard(),
                    DesignSystem.verticalSpaceM,
                    _buildNetworkCard(),
                    DesignSystem.verticalSpaceM,
                    _buildBatteryCard(),
                    DesignSystem.verticalSpaceXL,
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildOverallScoreCard(BuildContext context) {
    final score = _report.overallScore;
    final grade = _report.performanceGrade;
    final color = _getScoreColor(score, context);

    return Card(
      child: Padding(
        padding: DesignSystem.cardContentPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Overall Performance',
                  style: TextStyle(
                    fontSize: DesignSystem.fontSizeXL,
                    fontWeight:
                        DesignSystem.getAdjustedFontWeight(FontWeight.bold),
                    color: DesignSystem.getSettingsTextColor(
                      context,
                      isPrimary: true,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal:
                        DesignSystem.spaceS + 4, // 12.0 equivalent (8.0 + 4.0)
                    vertical:
                        DesignSystem.spaceXS + 2, // 6.0 equivalent (4.0 + 2.0)
                  ),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(
                      DesignSystem.spaceM,
                    ), // 16.0 equivalent
                    border: Border.all(color: color),
                  ),
                  child: Text(
                    'Grade $grade',
                    style: TextStyle(
                      color: color,
                      fontWeight:
                          DesignSystem.getAdjustedFontWeight(FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
            DesignSystem.verticalSpaceM,
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: score / 100,
                    backgroundColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                ),
                DesignSystem.horizontalSpaceM,
                Text(
                  '${score.toStringAsFixed(1)}/100',
                  style: TextStyle(
                    fontSize: DesignSystem.fontSizeL,
                    fontWeight:
                        DesignSystem.getAdjustedFontWeight(FontWeight.bold),
                    color: color,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFrameRateCard() {
    final metrics = _report.frameRateMetrics;
    return _buildMetricCard(
      title: 'Frame Rate',
      icon: Icons.speed,
      children: [
        _buildMetricRow('Current FPS', metrics.currentFPS.toStringAsFixed(1)),
        _buildMetricRow('Average FPS', metrics.averageFPS.toStringAsFixed(1)),
        _buildMetricRow('Target FPS', metrics.targetFPS.toStringAsFixed(0)),
        _buildMetricRow('Jank Events', metrics.jankCount.toString()),
        _buildMetricRow('Jank Rate', '${metrics.jankRate.toStringAsFixed(1)}%'),
      ],
    );
  }

  Widget _buildStartupTimeCard() {
    final metrics = _report.startupMetrics;
    return _buildMetricCard(
      title: 'Startup Performance',
      icon: Icons.rocket_launch,
      children: [
        _buildMetricRow(
          'Total Time',
          '${metrics.totalStartupTime.inMilliseconds}ms',
        ),
        _buildMetricRow(
          'Time to First Frame',
          '${metrics.timeToFirstFrame.inMilliseconds}ms',
        ),
        _buildMetricRow('Startup Type', metrics.startupType.name),
        _buildMetricRow('Bottlenecks', '${metrics.bottlenecks.length}'),
        if (metrics.slowestPhase != null)
          _buildMetricRow(
            'Slowest Phase',
            '${metrics.slowestPhase!.name} (${metrics.slowestPhase!.duration?.inMilliseconds ?? 0}ms)',
          ),
      ],
    );
  }

  Widget _buildMemoryUsageCard() {
    final metrics = _report.memoryMetrics;
    return _buildMetricCard(
      title: 'Memory Usage',
      icon: Icons.memory,
      children: [
        _buildMetricRow('Current Usage', '${metrics.currentUsageMB}MB'),
        _buildMetricRow('Peak Usage', '${metrics.peakUsageMB}MB'),
        _buildMetricRow('Device Limit', '${metrics.deviceLimitMB}MB'),
        _buildMetricRow(
          'Usage Percentage',
          '${metrics.usagePercent.toStringAsFixed(1)}%',
        ),
        _buildMetricRow('Memory Spikes', '${metrics.memorySpikes.length}'),
        _buildMetricRow('Large Objects', '${metrics.largeObjectCount}'),
        _buildStatusRow('Memory Pressure', metrics.isMemoryPressure),
      ],
    );
  }

  Widget _buildCPUUsageCard() {
    final metrics = _report.cpuMetrics;
    return _buildMetricCard(
      title: 'CPU Usage',
      icon: Icons.developer_board,
      children: [
        _buildMetricRow(
          'Current Usage',
          '${metrics.currentUsage.toStringAsFixed(1)}%',
        ),
        _buildMetricRow(
          'Average Usage',
          '${metrics.averageUsage.toStringAsFixed(1)}%',
        ),
        _buildMetricRow(
          'Peak Usage',
          '${metrics.peakUsage.toStringAsFixed(1)}%',
        ),
        _buildMetricRow('Background Tasks', '${metrics.backgroundTaskCount}'),
        _buildMetricRow('Hotspots', '${metrics.hotspots.length}'),
        _buildStatusRow('Idle State', metrics.isIdleState),
        if (metrics.mostSevereHotspot != null)
          _buildMetricRow(
            'Worst Hotspot',
            '${metrics.mostSevereHotspot!.methodName} (${metrics.mostSevereHotspot!.executionTime.inMilliseconds}ms)',
          ),
      ],
    );
  }

  Widget _buildNetworkCard() {
    final metrics = _report.networkMetrics;
    return _buildMetricCard(
      title: 'Network Performance',
      icon: Icons.network_check,
      children: [
        _buildMetricRow(
          'Avg Response Time',
          '${metrics.averageResponseTime.inMilliseconds}ms',
        ),
        _buildMetricRow('Total Requests', '${metrics.totalRequests}'),
        _buildMetricRow('Failed Requests', '${metrics.failedRequests}'),
        _buildMetricRow(
          'Success Rate',
          '${metrics.successRate.toStringAsFixed(1)}%',
        ),
      ],
    );
  }

  Widget _buildBatteryCard() {
    final metrics = _report.batteryMetrics;
    return _buildMetricCard(
      title: 'Battery Status',
      icon: Icons.battery_std,
      children: [
        _buildMetricRow('Current Level', '${metrics.currentLevel}%'),
        _buildMetricRow('Battery State', metrics.currentState.name),
        _buildStatusRow('Power Saving Mode', metrics.isPowerSavingMode),
        _buildStatusRow('Battery Healthy', metrics.isBatteryHealthy),
        _buildStatusRow('Low Battery', metrics.isLowBattery),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: DesignSystem.cardContentPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  size: DesignSystem.getAdjustedIconSize(
                    DesignSystem.widgetIconSizeMedium - 8,
                  ), // 20.0 equivalent (28.0 - 8.0)
                ),
                DesignSystem.horizontalSpaceS,
                Text(
                  title,
                  style: TextStyle(
                    fontSize: DesignSystem.fontSizeL,
                    fontWeight:
                        DesignSystem.getAdjustedFontWeight(FontWeight.bold),
                    color: DesignSystem.getSettingsTextColor(
                      context,
                      isPrimary: true,
                    ),
                  ),
                ),
              ],
            ),
            DesignSystem.verticalSpaceM,
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceTiny),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: true),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, bool status) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceTiny),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color:
                  DesignSystem.getSettingsTextColor(context, isPrimary: false),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(
                horizontal: DesignSystem.spaceS,
                vertical: DesignSystem.spaceTiny),
            decoration: BoxDecoration(
              color: status
                  ? Theme.of(context)
                      .colorScheme
                      .primaryContainer
                      .withValues(alpha: 0.3)
                  : Theme.of(context)
                      .colorScheme
                      .errorContainer
                      .withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(DesignSystem.radiusML),
            ),
            child: Text(
              status ? 'Yes' : 'No',
              style: TextStyle(
                color: status
                    ? DesignSystem.getSettingsTextColor(
                        context,
                        isPrimary: true,
                      )
                    : Theme.of(context).colorScheme.error,
                fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
                fontSize: DesignSystem.fontSizeS,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getScoreColor(double score, BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    if (score >= 90) return colorScheme.primary;
    if (score >= 80) return colorScheme.secondary;
    if (score >= 70) return colorScheme.tertiary;
    if (score >= 60) return colorScheme.outline;
    return colorScheme.error;
  }
}
