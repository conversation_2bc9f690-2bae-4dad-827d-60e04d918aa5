import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/page/home_page/statistics_page.dart';
import 'package:dasso_reader/widgets/common/adaptive_navigation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class StatisticsSection extends StatelessWidget {
  const StatisticsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: const Icon(Icons.show_chart),
      title: Text(L10n.of(context).navBar_statistics),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        AdaptiveNavigation.push(
          context,
          Scaffold(
            appBar: AppBar(
              leading: Icon<PERSON>utton(
                icon: const Icon(Icons.arrow_back),
                tooltip: 'Back to settings',
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              title: Text(L10n.of(context).navBar_statistics),
            ),
            body: const StatisticPage(),
          ),
        );
      },
    );
  }
}
